import React, { useState } from 'react';
import {
  adaptPersonnelToBackend,
  adaptBadgeToBackend,
  validatePersonnelData,
  validateBadgeData
} from '../services/dataAdapter';

export function TestDataAdapter() {
  const [results, setResults] = useState([]);

  const runTests = () => {
    const testResults = [];

    // Test 1: Militaire interne
    const militaireInterneData = {
      type: 'militaire_interne',
      nom: 'Du<PERSON>',
      prenom: '<PERSON>',
      matricule: 'M12345',
      grade: '5',
      unite: '2'
    };

    const validation1 = validatePersonnelData(militaireInterneData);
    const adapted1 = validation1.isValid ? adaptPersonnelToBackend(militaireInterneData) : null;
    
    testResults.push({
      test: 'Militaire Interne',
      original: militaireInterneData,
      validation: validation1,
      adapted: adapted1
    });

    // Test 2: Militaire externe
    const militaireExterneData = {
      type: 'militaire_externe',
      nom: '<PERSON>',
      prenom: '<PERSON>',
      matricule: 'ME67890',
      cin: '12345678',
      grade: '3',
      unite_origine: '1',
      destination: '2',
      heure_entree: '2024-01-15T08:00:00',
      objet_visite: 'Réunion de coordination',
      badge_id: '10'
    };

    const validation2 = validatePersonnelData(militaireExterneData);
    const adapted2 = validation2.isValid ? adaptPersonnelToBackend(militaireExterneData) : null;
    
    testResults.push({
      test: 'Militaire Externe',
      original: militaireExterneData,
      validation: validation2,
      adapted: adapted2
    });

    // Test 3: Badge création
    const badgeCreationData = {
      type: 'badge_visiteur_militaire'
    };

    const validation3 = validateBadgeData(badgeCreationData, true);
    const adapted3 = validation3.isValid ? adaptBadgeToBackend(badgeCreationData, true) : null;
    
    testResults.push({
      test: 'Badge Création',
      original: badgeCreationData,
      validation: validation3,
      adapted: adapted3
    });

    // Test 4: Données invalides
    const invalidData = {
      type: 'militaire_interne',
      nom: '',
      prenom: 'Jean',
      grade: 'invalid',
      unite: ''
    };

    const validation4 = validatePersonnelData(invalidData);
    
    testResults.push({
      test: 'Données Invalides',
      original: invalidData,
      validation: validation4,
      adapted: null
    });

    setResults(testResults);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h2 className="text-xl font-bold mb-4">Test des Adaptations de Données</h2>
      
      <button
        onClick={runTests}
        className="mb-6 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        Exécuter les Tests
      </button>

      {results.length > 0 && (
        <div className="space-y-6">
          {results.map((result, index) => (
            <div key={index} className="border border-gray-200 rounded p-4">
              <h3 className="font-semibold text-lg mb-2">{result.test}</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-700 mb-1">Données originales:</h4>
                  <pre className="bg-gray-100 p-2 rounded text-sm overflow-x-auto">
                    {JSON.stringify(result.original, null, 2)}
                  </pre>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-700 mb-1">Validation:</h4>
                  <div className={`p-2 rounded text-sm ${result.validation.isValid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    <div>Valide: {result.validation.isValid ? '✅' : '❌'}</div>
                    {result.validation.errors.length > 0 && (
                      <div className="mt-1">
                        <strong>Erreurs:</strong>
                        <ul className="list-disc list-inside">
                          {result.validation.errors.map((error, i) => (
                            <li key={i}>{error}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              {result.adapted && (
                <div className="mt-4">
                  <h4 className="font-medium text-gray-700 mb-1">Données adaptées pour le backend:</h4>
                  <pre className="bg-blue-50 p-2 rounded text-sm overflow-x-auto">
                    {JSON.stringify(result.adapted, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
