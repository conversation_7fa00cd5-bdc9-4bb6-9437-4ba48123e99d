const express = require('express');
const router = express.Router();
const { validationMiddleware, schemas } = require('../utils/validation');
const {
  listerBadges,
  obtenirBadgesDisponibles,
  creerBadgeVisiteur,
  obtenirBadge,
  listerAttributions,
  cloturerAttribution,
  toggleBadgeStatut
} = require('../controllers/badgeController');

/**
 * @route GET /api/badges
 * @desc Lister tous les badges avec filtres et pagination
 * @access Public (à sécuriser plus tard)
 * @query type - Type de badge (badge_militaire_interne, badge_visiteur_militaire, badge_visiteur_civil)
 * @query statut - Statut du badge (actif, inactif)
 * @query page - Numéro de page (défaut: 1)
 * @query limit - Nombre d'éléments par page (défaut: 10)
 */
router.get('/', listerBadges);

/**
 * @route GET /api/badges/disponibles/:type
 * @desc Récupérer les badges visiteurs disponibles par type
 * @access Public (à sécuriser plus tard)
 * @param type - Type de badge (badge_visiteur_militaire ou badge_visiteur_civil)
 */
router.get('/disponibles/:type', obtenirBadgesDisponibles);

/**
 * @route POST /api/badges/visiteurs
 * @desc Créer un nouveau badge visiteur
 * @access Public (à sécuriser plus tard)
 * @body type_badge - Type de badge visiteur à créer
 */
router.post('/visiteurs', 
  validationMiddleware(schemas.badgeCreation),
  creerBadgeVisiteur
);

/**
 * @route GET /api/badges/:id
 * @desc Récupérer un badge par ID avec son historique
 * @access Public (à sécuriser plus tard)
 */
router.get('/:id', obtenirBadge);

/**
 * @route PUT /api/badges/:id/toggle-statut
 * @desc Activer/Désactiver un badge
 * @access Public (à sécuriser plus tard)
 */
router.put('/:id/toggle-statut', toggleBadgeStatut);

/**
 * @route GET /api/badges/attributions/list
 * @desc Lister toutes les attributions avec filtres
 * @access Public (à sécuriser plus tard)
 * @query statut - Statut de l'attribution (actif, expire, desactive)
 * @query type_personnel - Type de personnel (militaire_interne, militaire_externe, civil_externe)
 * @query page - Numéro de page (défaut: 1)
 * @query limit - Nombre d'éléments par page (défaut: 10)
 */
router.get('/attributions/list', listerAttributions);

/**
 * @route PUT /api/badges/attributions/:id/cloturer
 * @desc Clôturer une attribution de badge visiteur
 * @access Public (à sécuriser plus tard)
 */
router.put('/attributions/:id/cloturer', cloturerAttribution);

/**
 * @route GET /api/badges/dashboard/stats
 * @desc Récupérer les statistiques des badges pour le dashboard
 * @access Public (à sécuriser plus tard)
 * @returns Statistiques simplifiées pour le dashboard
 */
router.get('/dashboard/stats', async (req, res) => {
  try {
    const { Badge, TypeBadge, AttributionBadge } = require('../models');

    // Compter tous les badges
    const totalBadges = await Badge.count();

    // Badges actifs
    const badgesActifs = await Badge.count({ where: { actif: true } });

    // Badges avec attributions actives (considérés comme "attribués")
    const badgesAttribues = await Badge.count({
      include: [{
        model: AttributionBadge,
        as: 'attributions',
        where: { statut: 'actif' },
        required: true
      }]
    });

    // Badges visiteurs disponibles (actifs mais non attribués)
    const badgesVisiteursDisponibles = await Badge.count({
      where: {
        actif: true,
        permanent: false
      },
      include: [{
        model: AttributionBadge,
        as: 'attributions',
        where: { statut: 'actif' },
        required: false
      }],
      having: require('sequelize').literal('COUNT("attributions"."id") = 0')
    });

    res.json({
      total: totalBadges,
      actifs: badgesActifs,
      attribues: badgesAttribues,
      disponibles: badgesVisiteursDisponibles
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des stats badges:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des statistiques badges',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route GET /api/badges/management/list
 * @desc Récupérer la liste complète des badges pour la gestion
 * @access Public (à sécuriser plus tard)
 * @returns Liste complète avec tous les détails pour la gestion
 */
router.get('/management/list', async (req, res) => {
  try {
    const { type, statut, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { Badge, TypeBadge, AttributionBadge, Personnel } = require('../models');

    let whereClause = {};
    let includeClause = [
      {
        model: TypeBadge,
        as: 'typeBadge',
        attributes: ['nom_type_badge']
      },
      {
        model: AttributionBadge,
        as: 'attributions',
        include: [{
          model: Personnel,
          as: 'personnel',
          attributes: ['nom', 'prenom', 'matricule', 'cin']
        }],
        where: { statut: 'actif' },
        required: false
      }
    ];

    // Filtre par type de badge
    if (type && type !== 'all') {
      includeClause[0].where = { nom_type_badge: type };
    }

    // Filtre par statut
    if (statut && statut !== 'all') {
      if (statut === 'actif') {
        whereClause.actif = true;
      } else if (statut === 'inactif') {
        whereClause.actif = false;
      }
    }

    const { count, rows } = await Badge.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    const result = rows.map(badge => {
      const attribution = badge.attributions && badge.attributions.length > 0 ? badge.attributions[0] : null;

      return {
        id: badge.id,
        numero: badge.epc_code,
        type: badge.typeBadge.nom_type_badge,
        permanent: badge.permanent,
        actif: badge.actif,
        statut: !badge.actif ? 'desactive' :
                (attribution ? 'attribue' :
                 (badge.permanent ? 'actif' : 'disponible')),
        personnel: attribution ? {
          nom: attribution.personnel.nom,
          prenom: attribution.personnel.prenom,
          matricule: attribution.personnel.matricule,
          cin: attribution.personnel.cin
        } : null,
        date_creation: badge.created_at
      };
    });

    res.json({
      badges: result,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de la liste badges:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération de la liste badges',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route GET /api/badges/assignments/active
 * @desc Récupérer les attributions actives pour la page ActiveAssignments
 * @access Public (à sécuriser plus tard)
 * @returns Liste des attributions actives avec détails complets
 */
router.get('/assignments/active', async (req, res) => {
  try {
    const { statut = 'actif', type_personnel, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { AttributionBadge, Badge, Personnel, TypePersonnel, TypeBadge, Unite } = require('../models');

    let whereClause = { statut };
    let includeClause = [
      {
        model: Badge,
        as: 'badge',
        attributes: ['epc_code', 'permanent'],
        include: [{
          model: TypeBadge,
          as: 'typeBadge',
          attributes: ['nom_type_badge']
        }]
      },
      {
        model: Personnel,
        as: 'personnel',
        attributes: ['nom', 'prenom', 'matricule', 'cin'],
        include: [
          {
            model: TypePersonnel,
            as: 'typePersonnel',
            attributes: ['nom_type']
          },
          {
            model: Unite,
            as: 'unite',
            attributes: ['nom_unite', 'code_unite'],
            required: false
          }
        ]
      }
    ];

    // Filtre par type de personnel
    if (type_personnel && type_personnel !== 'all') {
      includeClause[1].include[0].where = { nom_type: type_personnel };
    }

    const { count, rows } = await AttributionBadge.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['date_attribution', 'DESC']]
    });

    const result = rows.map(attribution => {
      const dureeHeures = attribution.date_fin ?
        Math.round((new Date(attribution.date_fin) - new Date(attribution.date_attribution)) / (1000 * 60 * 60)) :
        null;

      const tempsEcoule = Math.round((new Date() - new Date(attribution.date_attribution)) / (1000 * 60 * 60));

      return {
        id: attribution.id,
        badge_numero: attribution.badge.epc_code,
        badge_type: attribution.badge.typeBadge.nom_type_badge,
        badge_permanent: attribution.badge.permanent,
        personnel_nom: attribution.personnel.nom,
        personnel_prenom: attribution.personnel.prenom,
        personnel_matricule: attribution.personnel.matricule,
        personnel_cin: attribution.personnel.cin,
        personnel_type: attribution.personnel.typePersonnel.nom_type,
        unite: attribution.personnel.unite ? {
          nom: attribution.personnel.unite.nom_unite,
          code: attribution.personnel.unite.code_unite
        } : null,
        date_attribution: attribution.date_attribution,
        date_fin: attribution.date_fin,
        duree_prevue_heures: dureeHeures,
        temps_ecoule_heures: tempsEcoule,
        alerte_duree: dureeHeures && tempsEcoule > dureeHeures * 0.8, // Alerte à 80% du temps
        statut: attribution.statut
      };
    });

    res.json({
      attributions: result,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des attributions actives:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des attributions actives',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route GET /api/badges/form/available-visitors
 * @desc Récupérer les badges visiteurs disponibles pour les formulaires
 * @access Public (à sécuriser plus tard)
 * @returns Liste des badges visiteurs disponibles pour attribution
 */
router.get('/form/available-visitors', async (req, res) => {
  try {
    const { Badge, TypeBadge, AttributionBadge } = require('../models');

    const badgesDisponibles = await Badge.findAll({
      where: {
        actif: true,
        permanent: false
      },
      include: [
        {
          model: TypeBadge,
          as: 'typeBadge',
          attributes: ['nom_type_badge'],
          where: {
            nom_type_badge: ['badge_visiteur_militaire', 'badge_visiteur_civil']
          }
        },
        {
          model: AttributionBadge,
          as: 'attributions',
          where: { statut: 'actif' },
          required: false
        }
      ],
      having: require('sequelize').literal('COUNT("attributions"."id") = 0'),
      order: [['epc_code', 'ASC']]
    });

    const result = badgesDisponibles.map(badge => ({
      id: badge.id,
      numero: badge.epc_code,
      type: badge.typeBadge.nom_type_badge
    }));

    res.json(result);
  } catch (error) {
    console.error('Erreur lors de la récupération des badges visiteurs disponibles:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des badges visiteurs disponibles',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
