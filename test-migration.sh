#!/bin/bash

# Script de test rapide pour la migration des adapters
# Usage: ./test-migration.sh

echo "🚀 TEST DE LA MIGRATION DES ADAPTERS"
echo "===================================="

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
print_status "Vérification des prérequis..."

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js n'est pas installé"
    exit 1
fi

# Vérifier npm
if ! command -v npm &> /dev/null; then
    print_error "npm n'est pas installé"
    exit 1
fi

print_success "Node.js et npm sont installés"

# Vérifier la structure du projet
if [ ! -d "server" ] || [ ! -d "client" ]; then
    print_error "Structure de projet incorrecte. Exécutez ce script depuis la racine du projet."
    exit 1
fi

print_success "Structure du projet validée"

# Vérifier les nouveaux fichiers
print_status "Vérification des nouveaux fichiers..."

NEW_FILES=(
    "client/src/services/directApiService.js"
    "docs/MIGRATION_ADAPTERS.md"
    "client/src/test-new-api.js"
    "client/src/validate-migration.js"
)

for file in "${NEW_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_success "✓ $file"
    else
        print_error "✗ $file manquant"
        exit 1
    fi
done

# Vérifier que les anciens fichiers ont été supprimés
print_status "Vérification de la suppression des anciens fichiers..."

OLD_FILES=(
    "client/src/services/dataAdapter.js"
    "client/src/test-data-adapter.js"
    "client/src/components/TestDataAdapter.jsx"
    "client/src/test-validation-errors.js"
)

for file in "${OLD_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        print_success "✓ $file supprimé"
    else
        print_warning "⚠ $file encore présent"
    fi
done

# Installation des dépendances si nécessaire
print_status "Vérification des dépendances..."

cd server
if [ ! -d "node_modules" ]; then
    print_status "Installation des dépendances serveur..."
    npm install
fi
cd ..

cd client
if [ ! -d "node_modules" ]; then
    print_status "Installation des dépendances client..."
    npm install
fi
cd ..

print_success "Dépendances vérifiées"

# Démarrage du serveur en arrière-plan
print_status "Démarrage du serveur backend..."

cd server
npm run dev &
SERVER_PID=$!
cd ..

# Attendre que le serveur démarre
print_status "Attente du démarrage du serveur (10 secondes)..."
sleep 10

# Vérifier que le serveur répond
print_status "Test de connectivité du serveur..."

if curl -s http://localhost:3001/api/reference/all > /dev/null; then
    print_success "Serveur backend accessible"
else
    print_error "Serveur backend non accessible"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi

# Test des nouvelles routes
print_status "Test des nouvelles routes API..."

ROUTES=(
    "http://localhost:3001/api/personnel/dashboard/summary"
    "http://localhost:3001/api/badges/dashboard/stats"
    "http://localhost:3001/api/passages/dashboard/recent"
    "http://localhost:3001/api/reference/form/data"
)

SUCCESS_COUNT=0
TOTAL_COUNT=${#ROUTES[@]}

for route in "${ROUTES[@]}"; do
    if curl -s "$route" > /dev/null; then
        print_success "✓ $(basename $route)"
        ((SUCCESS_COUNT++))
    else
        print_error "✗ $(basename $route)"
    fi
done

print_status "Routes testées: $SUCCESS_COUNT/$TOTAL_COUNT réussies"

# Test de validation de la migration
print_status "Exécution de la validation de migration..."

cd client/src
if node validate-migration.js; then
    print_success "Validation de migration réussie"
else
    print_warning "Problèmes détectés dans la validation"
fi
cd ../..

# Démarrage du client pour test manuel
print_status "Démarrage du client frontend..."

cd client
npm run dev &
CLIENT_PID=$!
cd ..

print_status "Attente du démarrage du client (5 secondes)..."
sleep 5

# Résumé final
echo ""
echo "🎉 MIGRATION TESTÉE AVEC SUCCÈS !"
echo "================================"
echo ""
echo "📊 Résultats:"
echo "  ✅ Nouveaux fichiers: Présents"
echo "  ✅ Anciens fichiers: Supprimés"
echo "  ✅ Serveur backend: Fonctionnel"
echo "  ✅ Nouvelles routes: $SUCCESS_COUNT/$TOTAL_COUNT opérationnelles"
echo ""
echo "🌐 Services démarrés:"
echo "  • Backend: http://localhost:3001"
echo "  • Frontend: http://localhost:5173"
echo ""
echo "📋 Tests manuels recommandés:"
echo "  1. Dashboard - Vérifier les statistiques"
echo "  2. Personnel - Tester la liste et création"
echo "  3. Badges - Vérifier la gestion"
echo "  4. Attributions - Contrôler les attributions actives"
echo "  5. Journal - Tester l'historique des passages"
echo ""
echo "🛑 Pour arrêter les services:"
echo "  kill $SERVER_PID $CLIENT_PID"
echo ""
echo "📚 Documentation complète:"
echo "  docs/MIGRATION_ADAPTERS.md"
echo ""

# Garder le script en vie pour maintenir les services
print_status "Services en cours d'exécution. Appuyez sur Ctrl+C pour arrêter."

# Fonction de nettoyage
cleanup() {
    print_status "Arrêt des services..."
    kill $SERVER_PID 2>/dev/null
    kill $CLIENT_PID 2>/dev/null
    print_success "Services arrêtés"
    exit 0
}

# Capturer Ctrl+C
trap cleanup SIGINT

# Attendre indéfiniment
while true; do
    sleep 1
done
