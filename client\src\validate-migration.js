// Script de validation de la migration des adapters
// Vérifie que tous les fichiers utilisent bien les nouvelles routes

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Fonction pour lire récursivement tous les fichiers .jsx et .js
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      getAllFiles(filePath, fileList);
    } else if (file.endsWith('.jsx') || file.endsWith('.js')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Fonction pour analyser un fichier
function analyzeFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(path.join(__dirname, '..'), filePath);
  
  const issues = [];
  
  // Vérifier les imports obsolètes
  if (content.includes("from './services/apiService'") && 
      !content.includes("from './services/directApiService'") &&
      !filePath.includes('apiServiceNew.js')) {
    issues.push({
      type: 'OLD_IMPORT',
      message: 'Utilise encore l\'ancien apiService au lieu de directApiService',
      line: content.split('\n').findIndex(line => line.includes("from './services/apiService'")) + 1
    });
  }
  
  // Vérifier les références aux adapters supprimés
  const obsoleteAdapters = [
    'adaptPersonnelFromBackend',
    'adaptPersonnelToBackend',
    'adaptBadgeFromBackend',
    'adaptBadgeToBackend',
    'adaptPassageFromBackend',
    'adaptAttributionFromBackend',
    'dataAdapter'
  ];
  
  obsoleteAdapters.forEach(adapter => {
    if (content.includes(adapter)) {
      issues.push({
        type: 'OBSOLETE_ADAPTER',
        message: `Référence à l'adapter obsolète: ${adapter}`,
        line: content.split('\n').findIndex(line => line.includes(adapter)) + 1
      });
    }
  });
  
  // Vérifier les appels d'API qui pourraient nécessiter une mise à jour
  const oldApiCalls = [
    'personnelService.getAll(',
    'badgeService.getAll(',
    'passageService.getAll(',
    'passageService.getRecent('
  ];
  
  oldApiCalls.forEach(call => {
    if (content.includes(call)) {
      issues.push({
        type: 'POTENTIAL_OLD_API',
        message: `Appel API potentiellement obsolète: ${call}`,
        line: content.split('\n').findIndex(line => line.includes(call)) + 1
      });
    }
  });
  
  return { relativePath, issues };
}

// Fonction principale de validation
function validateMigration() {
  console.log('🔍 VALIDATION DE LA MIGRATION DES ADAPTERS');
  console.log('=' .repeat(60));
  
  const clientDir = path.join(__dirname, '..');
  const files = getAllFiles(clientDir);
  
  let totalIssues = 0;
  const fileResults = [];
  
  files.forEach(filePath => {
    const result = analyzeFile(filePath);
    if (result.issues.length > 0) {
      fileResults.push(result);
      totalIssues += result.issues.length;
    }
  });
  
  // Affichage des résultats
  if (totalIssues === 0) {
    console.log('✅ Aucun problème détecté ! La migration semble complète.');
  } else {
    console.log(`⚠️  ${totalIssues} problème(s) détecté(s) dans ${fileResults.length} fichier(s):`);
    console.log('');
    
    fileResults.forEach(({ relativePath, issues }) => {
      console.log(`📄 ${relativePath}`);
      issues.forEach(issue => {
        const icon = issue.type === 'OLD_IMPORT' ? '🔄' : 
                    issue.type === 'OBSOLETE_ADAPTER' ? '❌' : '⚠️';
        console.log(`   ${icon} Ligne ${issue.line}: ${issue.message}`);
      });
      console.log('');
    });
  }
  
  // Vérification des fichiers supprimés
  console.log('🗑️  VÉRIFICATION DES FICHIERS SUPPRIMÉS');
  console.log('─'.repeat(40));
  
  const obsoleteFiles = [
    'src/services/dataAdapter.js',
    'src/test-data-adapter.js',
    'src/components/TestDataAdapter.jsx',
    'src/test-validation-errors.js'
  ];
  
  obsoleteFiles.forEach(file => {
    const fullPath = path.join(clientDir, file);
    if (fs.existsSync(fullPath)) {
      console.log(`❌ Fichier obsolète encore présent: ${file}`);
      totalIssues++;
    } else {
      console.log(`✅ Fichier obsolète supprimé: ${file}`);
    }
  });
  
  // Vérification des nouveaux fichiers
  console.log('\n📁 VÉRIFICATION DES NOUVEAUX FICHIERS');
  console.log('─'.repeat(40));
  
  const newFiles = [
    'src/services/directApiService.js',
    'src/services/migrationGuide.md'
  ];
  
  newFiles.forEach(file => {
    const fullPath = path.join(clientDir, file);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ Nouveau fichier présent: ${file}`);
    } else {
      console.log(`❌ Nouveau fichier manquant: ${file}`);
      totalIssues++;
    }
  });
  
  // Résumé final
  console.log('\n📊 RÉSUMÉ DE LA VALIDATION');
  console.log('=' .repeat(60));
  
  if (totalIssues === 0) {
    console.log('🎉 Migration complètement réussie !');
    console.log('✅ Tous les adapters ont été remplacés par des routes API directes');
    console.log('✅ Tous les fichiers obsolètes ont été supprimés');
    console.log('✅ Tous les nouveaux fichiers sont présents');
  } else {
    console.log(`⚠️  ${totalIssues} problème(s) restant(s) à corriger`);
    console.log('📝 Consultez les détails ci-dessus pour les corrections nécessaires');
  }
  
  return totalIssues === 0;
}

// Fonction pour générer un rapport détaillé
function generateReport() {
  console.log('\n📋 GÉNÉRATION DU RAPPORT DE MIGRATION');
  console.log('=' .repeat(60));
  
  const report = {
    timestamp: new Date().toISOString(),
    migration: 'Remplacement des adapters par des routes API directes',
    status: validateMigration() ? 'SUCCESS' : 'INCOMPLETE',
    changes: {
      backend: {
        newRoutes: [
          'GET /api/personnel/dashboard/summary',
          'GET /api/personnel/management/list',
          'GET /api/badges/dashboard/stats',
          'GET /api/badges/management/list',
          'GET /api/badges/form/available-visitors',
          'GET /api/badges/assignments/active',
          'GET /api/passages/dashboard/recent',
          'GET /api/passages/log/history',
          'GET /api/reference/form/data'
        ]
      },
      frontend: {
        newFiles: [
          'src/services/directApiService.js',
          'src/services/migrationGuide.md',
          'src/test-new-api.js',
          'src/validate-migration.js'
        ],
        removedFiles: [
          'src/services/dataAdapter.js',
          'src/test-data-adapter.js',
          'src/components/TestDataAdapter.jsx',
          'src/test-validation-errors.js'
        ],
        modifiedFiles: [
          'src/pages/Dashboard.jsx',
          'src/pages/PersonnelManagement.jsx',
          'src/pages/BadgeManagement.jsx',
          'src/pages/ActiveAssignments.jsx',
          'src/pages/AccessLog.jsx',
          'src/pages/Statistics.jsx',
          'src/components/PersonnelForm.jsx',
          'src/components/BadgeForm.jsx'
        ]
      }
    },
    benefits: [
      'Suppression de la complexité des adapters',
      'Routes API spécifiques à chaque cas d\'usage',
      'Données directement utilisables côté frontend',
      'Meilleure performance avec moins de transformations',
      'Code plus maintenable et lisible'
    ]
  };
  
  const reportPath = path.join(__dirname, 'migration-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`📄 Rapport généré: ${reportPath}`);
  return report;
}

// Exécution si le script est appelé directement
if (import.meta.url === `file://${process.argv[1]}`) {
  validateMigration();
  generateReport();
}

export { validateMigration, generateReport };
