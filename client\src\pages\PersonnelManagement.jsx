import { useState, useEffect } from 'react';
import { UserPlus, Search, Filter, Edit, Trash2 } from 'lucide-react';
import { personnelService } from '../services/directApiService';
import { Card, CardHeader, CardContent, CardTitle, Modal, DataTable, Alert } from '../components/ui';
import { PersonnelForm } from '../components/PersonnelForm';
import { ProtectedAction } from '../components/ProtectedAction';
import { useAuth } from '../contexts/AuthContext';

function PersonnelManagement() {
  const [personnels, setPersonnels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedPersonnel, setSelectedPersonnel] = useState(null);
  const [filterType, setFilterType] = useState('all');
  const { } = useAuth();

  const fetchPersonnels = async () => {
    try {
      setLoading(true);
      const response = await personnelService.getManagementList({
        type: filterType === 'all' ? undefined : filterType
      });
      setPersonnels(response.personnel || []);
      setError(null);
    } catch (err) {
      setError('Erreur lors du chargement des personnels');
      console.error('Error fetching personnels:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPersonnels();
  }, []);

  const handleAddPersonnel = () => {
    setSelectedPersonnel(null);
    setShowModal(true);
  };

  const handleEditPersonnel = (personnel) => {
    setSelectedPersonnel(personnel);
    setShowModal(true);
  };

  const handleDeletePersonnel = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce personnel ?')) {
      try {
        await personnelService.delete(id);
        fetchPersonnels();
      } catch {
        setError('Erreur lors de la suppression');
      }
    }
  };

  const handleFormSuccess = () => {
    setShowModal(false);
    fetchPersonnels();
  };

  const filteredPersonnels = personnels.filter(personnel => {
    if (filterType === 'all') return true;
    return personnel.type === filterType;
  });

  const columns = [
    {
      header: 'Nom',
      accessor: 'nom',
      render: (personnel) => (
        <div>
          <div className="font-medium text-gray-900">{personnel.nom} {personnel.prenom}</div>
          <div className="text-sm text-gray-500">{personnel.matricule || personnel.cin}</div>
        </div>
      )
    },
    {
      header: 'Type',
      accessor: 'type',
      render: (personnel) => {
        const typeLabels = {
          militaire_interne: 'Militaire Interne',
          militaire_externe: 'Militaire Externe',
          civil_externe: 'Civil Externe'
        };
        const typeColors = {
          militaire_interne: 'bg-blue-100 text-blue-800',
          militaire_externe: 'bg-green-100 text-green-800',
          civil_externe: 'bg-purple-100 text-purple-800'
        };
        return (
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${typeColors[personnel.type]}`}>
            {typeLabels[personnel.type]}
          </span>
        );
      }
    },
    {
      header: 'Grade/Fonction',
      accessor: 'grade',
      render: (personnel) => personnel.grade || personnel.fonction || '-'
    },
    {
      header: 'Unité',
      accessor: 'unite',
      render: (personnel) => personnel.unite || personnel.destination || '-'
    },
    {
      header: 'Badge',
      accessor: 'badge_id',
      render: (personnel) => personnel.badge_id ? `Badge #${personnel.badge_id}` : 'Non attribué'
    },
    {
      header: 'Actions',
      render: (personnel) => (
        <div className="flex space-x-2">
          <ProtectedAction permission="write">
            <button
              onClick={() => handleEditPersonnel(personnel)}
              className="text-blue-600 hover:text-blue-800"
              title="Modifier"
            >
              <Edit className="h-4 w-4" />
            </button>
          </ProtectedAction>
          <ProtectedAction permission="delete">
            <button
              onClick={() => handleDeletePersonnel(personnel.id)}
              className="text-red-600 hover:text-red-800"
              title="Supprimer"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </ProtectedAction>
        </div>
      )
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Chargement...</div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">Gestion du Personnel</h1>
        <ProtectedAction permission="write">
          <button
            onClick={handleAddPersonnel}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
          >
            <UserPlus className="h-4 w-4" />
            <span>Nouveau Personnel</span>
          </button>
        </ProtectedAction>
      </div>

      {error && (
        <Alert variant="error" className="mb-6" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Liste du Personnel</CardTitle>
            <div className="flex items-center space-x-4">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">Tous les types</option>
                <option value="militaire_interne">Militaires Internes</option>
                <option value="militaire_externe">Militaires Externes</option>
                <option value="civil_externe">Civils Externes</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <DataTable
            data={filteredPersonnels}
            columns={columns}
            searchable={true}
            sortable={true}
            pagination={true}
            pageSize={10}
          />
        </CardContent>
      </Card>

      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={selectedPersonnel ? 'Modifier Personnel' : 'Nouveau Personnel'}
        size="lg"
      >
        <PersonnelForm
          personnel={selectedPersonnel}
          onSuccess={handleFormSuccess}
          onCancel={() => setShowModal(false)}
        />
      </Modal>
    </div>
  );
}

export default PersonnelManagement;
