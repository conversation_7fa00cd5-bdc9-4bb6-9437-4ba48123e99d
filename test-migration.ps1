# Script PowerShell de test rapide pour la migration des adapters
# Usage: .\test-migration.ps1

Write-Host "🚀 TEST DE LA MIGRATION DES ADAPTERS" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Fonctions pour l'affichage coloré
function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Vérification des prérequis
Write-Status "Vérification des prérequis..."

# Vérifier Node.js
try {
    $nodeVersion = node --version
    Write-Success "Node.js installé: $nodeVersion"
} catch {
    Write-Error "Node.js n'est pas installé"
    exit 1
}

# Vérifier npm
try {
    $npmVersion = npm --version
    Write-Success "npm installé: $npmVersion"
} catch {
    Write-Error "npm n'est pas installé"
    exit 1
}

# Vérifier la structure du projet
if (-not (Test-Path "server") -or -not (Test-Path "client")) {
    Write-Error "Structure de projet incorrecte. Exécutez ce script depuis la racine du projet."
    exit 1
}

Write-Success "Structure du projet validée"

# Vérifier les nouveaux fichiers
Write-Status "Vérification des nouveaux fichiers..."

$newFiles = @(
    "client\src\services\directApiService.js",
    "docs\MIGRATION_ADAPTERS.md",
    "client\src\test-new-api.js",
    "client\src\validate-migration.js"
)

foreach ($file in $newFiles) {
    if (Test-Path $file) {
        Write-Success "✓ $file"
    } else {
        Write-Error "✗ $file manquant"
        exit 1
    }
}

# Vérifier que les anciens fichiers ont été supprimés
Write-Status "Vérification de la suppression des anciens fichiers..."

$oldFiles = @(
    "client\src\services\dataAdapter.js",
    "client\src\test-data-adapter.js",
    "client\src\components\TestDataAdapter.jsx",
    "client\src\test-validation-errors.js"
)

foreach ($file in $oldFiles) {
    if (-not (Test-Path $file)) {
        Write-Success "✓ $file supprimé"
    } else {
        Write-Warning "⚠ $file encore présent"
    }
}

# Installation des dépendances si nécessaire
Write-Status "Vérification des dépendances..."

Set-Location server
if (-not (Test-Path "node_modules")) {
    Write-Status "Installation des dépendances serveur..."
    npm install
}
Set-Location ..

Set-Location client
if (-not (Test-Path "node_modules")) {
    Write-Status "Installation des dépendances client..."
    npm install
}
Set-Location ..

Write-Success "Dépendances vérifiées"

# Démarrage du serveur en arrière-plan
Write-Status "Démarrage du serveur backend..."

Set-Location server
$serverProcess = Start-Process -FilePath "npm" -ArgumentList "run", "dev" -PassThru -WindowStyle Hidden
Set-Location ..

# Attendre que le serveur démarre
Write-Status "Attente du démarrage du serveur (10 secondes)..."
Start-Sleep -Seconds 10

# Vérifier que le serveur répond
Write-Status "Test de connectivité du serveur..."

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/reference/all" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Success "Serveur backend accessible"
    } else {
        throw "Status code: $($response.StatusCode)"
    }
} catch {
    Write-Error "Serveur backend non accessible: $_"
    Stop-Process -Id $serverProcess.Id -Force -ErrorAction SilentlyContinue
    exit 1
}

# Test des nouvelles routes
Write-Status "Test des nouvelles routes API..."

$routes = @(
    "http://localhost:3001/api/personnel/dashboard/summary",
    "http://localhost:3001/api/badges/dashboard/stats",
    "http://localhost:3001/api/passages/dashboard/recent",
    "http://localhost:3001/api/reference/form/data"
)

$successCount = 0
$totalCount = $routes.Count

foreach ($route in $routes) {
    try {
        $response = Invoke-WebRequest -Uri $route -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            $routeName = ($route -split '/')[-1]
            Write-Success "✓ $routeName"
            $successCount++
        } else {
            throw "Status code: $($response.StatusCode)"
        }
    } catch {
        $routeName = ($route -split '/')[-1]
        Write-Error "✗ $routeName"
    }
}

Write-Status "Routes testées: $successCount/$totalCount réussies"

# Test de validation de la migration
Write-Status "Exécution de la validation de migration..."

Set-Location client\src
try {
    $validationResult = node validate-migration.js
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Validation de migration réussie"
    } else {
        Write-Warning "Problèmes détectés dans la validation"
    }
} catch {
    Write-Warning "Erreur lors de la validation: $_"
}
Set-Location ..\..

# Démarrage du client pour test manuel
Write-Status "Démarrage du client frontend..."

Set-Location client
$clientProcess = Start-Process -FilePath "npm" -ArgumentList "run", "dev" -PassThru -WindowStyle Hidden
Set-Location ..

Write-Status "Attente du démarrage du client (5 secondes)..."
Start-Sleep -Seconds 5

# Résumé final
Write-Host ""
Write-Host "🎉 MIGRATION TESTÉE AVEC SUCCÈS !" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Résultats:" -ForegroundColor Cyan
Write-Host "  ✅ Nouveaux fichiers: Présents" -ForegroundColor Green
Write-Host "  ✅ Anciens fichiers: Supprimés" -ForegroundColor Green
Write-Host "  ✅ Serveur backend: Fonctionnel" -ForegroundColor Green
Write-Host "  ✅ Nouvelles routes: $successCount/$totalCount opérationnelles" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Services démarrés:" -ForegroundColor Cyan
Write-Host "  • Backend: http://localhost:3001" -ForegroundColor White
Write-Host "  • Frontend: http://localhost:5173" -ForegroundColor White
Write-Host ""
Write-Host "📋 Tests manuels recommandés:" -ForegroundColor Cyan
Write-Host "  1. Dashboard - Vérifier les statistiques" -ForegroundColor White
Write-Host "  2. Personnel - Tester la liste et création" -ForegroundColor White
Write-Host "  3. Badges - Vérifier la gestion" -ForegroundColor White
Write-Host "  4. Attributions - Contrôler les attributions actives" -ForegroundColor White
Write-Host "  5. Journal - Tester l'historique des passages" -ForegroundColor White
Write-Host ""
Write-Host "🛑 Pour arrêter les services:" -ForegroundColor Yellow
Write-Host "  Stop-Process -Id $($serverProcess.Id), $($clientProcess.Id) -Force" -ForegroundColor White
Write-Host ""
Write-Host "📚 Documentation complète:" -ForegroundColor Cyan
Write-Host "  docs\MIGRATION_ADAPTERS.md" -ForegroundColor White
Write-Host ""

# Ouvrir automatiquement le navigateur
Write-Status "Ouverture du navigateur..."
Start-Process "http://localhost:5173"

Write-Status "Services en cours d'exécution. Appuyez sur Ctrl+C pour arrêter."

# Fonction de nettoyage
function Cleanup {
    Write-Status "Arrêt des services..."
    Stop-Process -Id $serverProcess.Id -Force -ErrorAction SilentlyContinue
    Stop-Process -Id $clientProcess.Id -Force -ErrorAction SilentlyContinue
    Write-Success "Services arrêtés"
    exit 0
}

# Capturer Ctrl+C
$null = Register-EngineEvent PowerShell.Exiting -Action { Cleanup }

# Attendre indéfiniment
try {
    while ($true) {
        Start-Sleep -Seconds 1
    }
} finally {
    Cleanup
}
