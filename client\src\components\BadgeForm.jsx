import { useState, useEffect } from 'react';
import { badgeService } from '../services/directApiService';
import { Alert } from './ui';

export function BadgeForm({ badge, onSuccess, onCancel }) {
  const [formData, setFormData] = useState({
    numero: '',
    type: 'militaire_interne',
    statut: 'actif',
    description: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (badge) {
      setFormData({
        numero: badge.numero || '',
        type: badge.type || 'militaire_interne',
        statut: badge.statut || 'actif',
        description: badge.description || ''
      });
    }
  }, [badge]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (badge) {
        await badgeService.update(badge.id, formData);
      } else {
        await badgeService.create(formData);
      }
      onSuccess();
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur lors de la sauvegarde');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert variant="error" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Numéro de badge *
        </label>
        <input
          type="text"
          name="numero"
          value={formData.numero}
          onChange={handleChange}
          required
          placeholder="Ex: B001, V001..."
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Type de badge *
        </label>
        <select
          name="type"
          value={formData.type}
          onChange={handleChange}
          required
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="militaire_interne">Militaire Interne</option>
          <option value="visiteur">Visiteur</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Statut *
        </label>
        <select
          name="statut"
          value={formData.statut}
          onChange={handleChange}
          required
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="actif">Actif</option>
          <option value="desactive">Désactivé</option>
          {badge && (
            <>
              <option value="attribue">Attribué</option>
              <option value="expire">Expiré</option>
            </>
          )}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          name="description"
          value={formData.description}
          onChange={handleChange}
          rows={3}
          placeholder="Description optionnelle du badge..."
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Annuler
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Sauvegarde...' : (badge ? 'Modifier' : 'Créer')}
        </button>
      </div>
    </form>
  );
}
