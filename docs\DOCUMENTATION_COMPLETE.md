# Documentation Complète - Système de Contrôle d'Accès Militaire

## Table des Matières

1. [Vue d'ensemble du projet](#vue-densemble-du-projet)
2. [Architecture générale](#architecture-générale)
3. [Base de données](#base-de-données)
4. [API Backend](#api-backend)
5. [Frontend React](#frontend-react)
6. [Configuration et déploiement](#configuration-et-déploiement)
7. [Tests et validation](#tests-et-validation)

---

## Vue d'ensemble du projet

### Description
Système de contrôle d'accès militaire utilisant la technologie RFID pour gérer les entrées et sorties du personnel militaire et civil dans un site sécurisé.

### Technologies utilisées
- **Backend**: Node.js, Express.js, Sequelize ORM
- **Base de données**: PostgreSQL
- **Frontend**: <PERSON>act 19, V<PERSON>, Tailwind CSS, TanStack Table
- **Authentification**: JWT (prévu)
- **Graphiques**: Recharts
- **Icons**: Lucide React

### Fonctionnalités principales
- Gestion du personnel (militaires internes/externes, civils)
- Gestion des badges RFID (permanents et temporaires)
- Enregistrement des passages en temps réel
- Tableau de bord avec statistiques
- Journal des accès avec filtres avancés
- Gestion des attributions de badges
- Rapports et statistiques détaillées

---

## Architecture générale

```
control-acces/
├── client/                 # Frontend React
├── server/                 # Backend Node.js/Express
├── database/              # Scripts SQL
├── docs/                  # Documentation
├── package.json           # Configuration racine
└── README.md
```

### Stack technique
- **Frontend**: React 19 + Vite + Tailwind CSS
- **Backend**: Node.js + Express + Sequelize
- **Base de données**: PostgreSQL
- **Communication**: REST API + Axios
- **Authentification**: JWT (en développement)

---

## Base de données

### Schéma de base de données (PostgreSQL)

#### Tables principales

##### 1. `type_personnel`
```sql
CREATE TABLE type_personnel (
    id SERIAL PRIMARY KEY,
    nom_type VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
**Valeurs**: `militaire_interne`, `militaire_externe`, `civil_externe`

##### 2. `grade`
```sql
CREATE TABLE grade (
    id SERIAL PRIMARY KEY,
    nom_grade VARCHAR(50) NOT NULL UNIQUE,
    niveau INTEGER NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
**Grades**: Soldat (1) → Général (9)

##### 3. `unite`
```sql
CREATE TABLE unite (
    id SERIAL PRIMARY KEY,
    nom_unite VARCHAR(100) NOT NULL,
    code_unite VARCHAR(20) UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
**Unités**: État-Major, Sécurité, Logistique, Communication, Maintenance

##### 4. `type_badge`
```sql
CREATE TABLE type_badge (
    id SERIAL PRIMARY KEY,
    nom_type_badge VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
**Types**: `badge_militaire_interne`, `badge_visiteur_militaire`, `badge_visiteur_civil`

##### 5. `porte`
```sql
CREATE TABLE porte (
    id SERIAL PRIMARY KEY,
    libelle VARCHAR(100) NOT NULL,
    description TEXT,
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### 6. `badge`
```sql
CREATE TABLE badge (
    id SERIAL PRIMARY KEY,
    epc_code VARCHAR(100) NOT NULL UNIQUE,
    id_type_badge INTEGER NOT NULL REFERENCES type_badge(id),
    permanent BOOLEAN DEFAULT FALSE,
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### 7. `personnel`
```sql
CREATE TABLE personnel (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    matricule VARCHAR(50) UNIQUE,
    cin VARCHAR(20) UNIQUE,
    id_type_personnel INTEGER NOT NULL REFERENCES type_personnel(id),
    id_grade INTEGER REFERENCES grade(id),
    id_unite INTEGER REFERENCES unite(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### 8. `militaire_externe_info`
```sql
CREATE TABLE militaire_externe_info (
    id_personnel INTEGER PRIMARY KEY REFERENCES personnel(id) ON DELETE CASCADE,
    horaire_entree TIMESTAMP,
    objet_visite TEXT,
    destination INTEGER REFERENCES unite(id),
    id_unite_origine INTEGER REFERENCES unite(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### 9. `civil_info`
```sql
CREATE TABLE civil_info (
    id_personnel INTEGER PRIMARY KEY REFERENCES personnel(id) ON DELETE CASCADE,
    horaire_entree TIMESTAMP,
    objet_visite TEXT,
    destination INTEGER REFERENCES unite(id),
    societe VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### 10. `attribution_badge`
```sql
CREATE TABLE attribution_badge (
    id SERIAL PRIMARY KEY,
    id_personnel INTEGER NOT NULL REFERENCES personnel(id),
    id_badge INTEGER NOT NULL REFERENCES badge(id),
    date_attribution TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_fin TIMESTAMP,
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'expire', 'desactive')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_badge_actif UNIQUE (id_badge, statut) DEFERRABLE INITIALLY DEFERRED
);
```

##### 11. `passage`
```sql
CREATE TABLE passage (
    id SERIAL PRIMARY KEY,
    id_badge INTEGER NOT NULL REFERENCES badge(id),
    id_porte INTEGER NOT NULL REFERENCES porte(id),
    date_acces TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    type_acces VARCHAR(10) NOT NULL CHECK (type_acces IN ('entree', 'sortie')),
    resultat VARCHAR(20) DEFAULT 'autorise' CHECK (resultat IN ('autorise', 'refuse', 'erreur')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Relations et contraintes

**Relations principales**:
- Personnel → TypePersonnel (N:1)
- Personnel → Grade (N:1)
- Personnel → Unite (N:1)
- Personnel → MilitaireExterneInfo (1:1)
- Personnel → CivilInfo (1:1)
- Badge → TypeBadge (N:1)
- AttributionBadge → Personnel (N:1)
- AttributionBadge → Badge (N:1)
- Passage → Badge (N:1)
- Passage → Porte (N:1)

**Index pour performance**:
```sql
CREATE INDEX idx_personnel_type ON personnel(id_type_personnel);
CREATE INDEX idx_personnel_matricule ON personnel(matricule);
CREATE INDEX idx_badge_epc ON badge(epc_code);
CREATE INDEX idx_attribution_statut ON attribution_badge(statut);
CREATE INDEX idx_attribution_personnel ON attribution_badge(id_personnel);
CREATE INDEX idx_passage_date ON passage(date_acces);
CREATE INDEX idx_passage_badge ON passage(id_badge);
```

**Triggers automatiques**:
- Mise à jour automatique de `updated_at` sur toutes les tables
- Fonction `update_updated_at_column()` pour la gestion des timestamps

---

## API Backend

### Configuration serveur

**Fichier**: `server/index.js`
- Port: 3001 (par défaut)
- Middleware: Helmet, CORS, Compression, Morgan
- Rate limiting: 1000 requêtes/15min en développement
- Parsing JSON: limite 10MB

### Structure des routes

#### 1. Routes Personnel (`/api/personnel`)

**Fichier**: `server/routes/personnel.js`

##### POST `/api/personnel/internes`
- **Description**: Créer un militaire interne avec badge permanent automatique
- **Body**: 
```json
{
  "nom": "string",
  "prenom": "string", 
  "matricule": "string",
  "id_grade": "integer",
  "id_unite": "integer"
}
```
- **Validation**: Schema `militaireInterne`
- **Contrôleur**: `creerMilitaireInterne`

##### POST `/api/personnel/militaires-externes`
- **Description**: Créer un militaire externe avec badge visiteur manuel
- **Body**:
```json
{
  "nom": "string",
  "prenom": "string",
  "matricule": "string",
  "id_grade": "integer",
  "id_unite": "integer",
  "horaire_entree": "datetime",
  "objet_visite": "string",
  "destination": "integer",
  "id_unite_origine": "integer"
}
```
- **Validation**: Schema `militaireExterne`
- **Contrôleur**: `creerMilitaireExterne`

##### POST `/api/personnel/civils-externes`
- **Description**: Créer un civil externe avec badge visiteur manuel
- **Body**:
```json
{
  "nom": "string",
  "prenom": "string",
  "cin": "string",
  "horaire_entree": "datetime",
  "objet_visite": "string",
  "destination": "integer",
  "societe": "string"
}
```
- **Validation**: Schema `civil`
- **Contrôleur**: `creerCivilExterne`

##### GET `/api/personnel`
- **Description**: Lister le personnel avec filtres et pagination
- **Query params**:
  - `type`: Type de personnel (militaire_interne, militaire_externe, civil_externe)
  - `page`: Numéro de page (défaut: 1)
  - `limit`: Nombre d'éléments par page (défaut: 10)
  - `search`: Recherche textuelle dans nom, prénom, matricule, CIN
- **Contrôleur**: `listerPersonnel`

##### GET `/api/personnel/:id`
- **Description**: Récupérer un personnel par ID
- **Contrôleur**: `obtenirPersonnel`

#### 2. Routes Badges (`/api/badges`)

**Fichier**: `server/routes/badges.js`

##### GET `/api/badges`
- **Description**: Lister tous les badges avec filtres et pagination
- **Query params**:
  - `type`: Type de badge
  - `statut`: Statut du badge (actif, inactif)
  - `page`: Numéro de page (défaut: 1)
  - `limit`: Nombre d'éléments par page (défaut: 10)
- **Contrôleur**: `listerBadges`

##### GET `/api/badges/disponibles/:type`
- **Description**: Récupérer les badges visiteurs disponibles par type
- **Params**: `type` - Type de badge (badge_visiteur_militaire ou badge_visiteur_civil)
- **Contrôleur**: `obtenirBadgesDisponibles`

##### POST `/api/badges/visiteurs`
- **Description**: Créer un nouveau badge visiteur
- **Body**:
```json
{
  "type_badge": "string"
}
```
- **Validation**: Schema `badgeCreation`
- **Contrôleur**: `creerBadgeVisiteur`

##### GET `/api/badges/:id`
- **Description**: Récupérer un badge par ID avec son historique
- **Contrôleur**: `obtenirBadge`

##### PUT `/api/badges/:id/toggle-statut`
- **Description**: Activer/Désactiver un badge
- **Contrôleur**: `toggleBadgeStatut`

##### GET `/api/badges/attributions/list`
- **Description**: Lister toutes les attributions avec filtres
- **Query params**:
  - `statut`: Statut de l'attribution (actif, expire, desactive)
  - `type_personnel`: Type de personnel
  - `page`: Numéro de page (défaut: 1)
  - `limit`: Nombre d'éléments par page (défaut: 10)
- **Contrôleur**: `listerAttributions`

##### PUT `/api/badges/attributions/:id/cloturer`
- **Description**: Clôturer une attribution de badge visiteur
- **Contrôleur**: `cloturerAttribution`

#### 3. Routes Passages (`/api/passages`)

**Fichier**: `server/routes/passages.js`

##### POST `/api/passages`
- **Description**: Enregistrer un nouveau passage RFID
- **Body**:
```json
{
  "epc_code": "string",
  "id_porte": "integer",
  "type_acces": "entree|sortie"
}
```
- **Validation**: Schema `passage`
- **Contrôleur**: `enregistrerPassage`

##### GET `/api/passages`
- **Description**: Récupérer l'historique des passages avec filtres
- **Query params**:
  - `page`: Numéro de page (défaut: 1)
  - `limit`: Nombre d'éléments par page (défaut: 20)
  - `type_acces`: Type d'accès (entree/sortie)
  - `resultat`: Résultat (autorise/refuse/erreur)
  - `id_porte`: ID de la porte
  - `date_debut`: Date de début (ISO)
  - `date_fin`: Date de fin (ISO)
  - `epc_code`: Code EPC du badge (recherche partielle)
  - `type_personnel`: Type de personnel
- **Contrôleur**: `obtenirHistoriquePassages`

##### GET `/api/passages/recent`
- **Description**: Récupérer les passages récents (temps réel)
- **Query params**:
  - `limit`: Nombre d'éléments à retourner (défaut: 10)
  - `minutes`: Période en minutes (défaut: 60)
- **Contrôleur**: `obtenirPassagesRecents`

##### GET `/api/passages/statistiques`
- **Description**: Obtenir les statistiques des passages
- **Query params**:
  - `periode`: Période en heures (défaut: 24)
- **Contrôleur**: `obtenirStatistiquesPassages`

##### GET `/api/passages/verifier/:epc_code`
- **Description**: Vérifier l'accès d'un badge (simulation lecteur RFID)
- **Params**: `epc_code` - Code EPC du badge à vérifier
- **Contrôleur**: `verifierAccesBadge`

#### 4. Routes Référence (`/api/reference`)

**Fichier**: `server/routes/reference.js`

##### GET `/api/reference/grades`
- **Description**: Récupérer tous les grades militaires
- **Contrôleur**: `obtenirGrades`

##### GET `/api/reference/unites`
- **Description**: Récupérer toutes les unités
- **Contrôleur**: `obtenirUnites`

##### GET `/api/reference/types-personnel`
- **Description**: Récupérer tous les types de personnel
- **Contrôleur**: `obtenirTypesPersonnel`

##### GET `/api/reference/types-badge`
- **Description**: Récupérer tous les types de badge
- **Contrôleur**: `obtenirTypesBadge`

##### GET `/api/reference/portes`
- **Description**: Récupérer toutes les portes actives
- **Contrôleur**: `obtenirPortes`

##### GET `/api/reference/all`
- **Description**: Récupérer toutes les données de référence
- **Contrôleur**: `obtenirToutesReferences`

#### 5. Routes Statistiques (`/api/statistiques`)

**Fichier**: `server/routes/statistiques.js`

##### GET `/api/statistiques/badges`
- **Description**: Obtenir les statistiques générales des badges
- **Contrôleur**: `obtenirStatistiquesBadges`
- **Réponse**:
```json
{
  "badges": {
    "total": "integer",
    "actifs": "integer",
    "permanents": "integer",
    "parType": {
      "militaire_interne": "integer",
      "visiteur_militaire": "integer",
      "visiteur_civil": "integer"
    },
    "attributionsActives": "integer",
    "visiteursDisponibles": "integer"
  },
  "personnel": {
    "avecBadgeActif": {
      "militaire_interne": "integer",
      "militaire_externe": "integer",
      "civil_externe": "integer"
    }
  }
}
```

##### GET `/api/statistiques/historique-attributions`
- **Description**: Obtenir l'historique des attributions récentes
- **Query params**:
  - `limit`: Nombre d'éléments à retourner (défaut: 20)
- **Contrôleur**: `obtenirHistoriqueAttributions`

##### GET `/api/statistiques/badges-expirants`
- **Description**: Obtenir les badges en fin de validité
- **Query params**:
  - `heures`: Nombre d'heures avant expiration (défaut: 24)
- **Contrôleur**: `obtenirBadgesExpirantBientot`

##### GET `/api/statistiques/rapport-utilisation`
- **Description**: Obtenir un rapport d'utilisation des badges
- **Query params**:
  - `periode`: Période en jours (défaut: 7)
- **Contrôleur**: `obtenirRapportUtilisation`

### Modèles Sequelize

#### Structure des modèles

**Fichier**: `server/models/index.js`

**Modèles disponibles**:
- `TypePersonnel`: Types de personnel
- `Grade`: Grades militaires
- `Unite`: Unités organisationnelles
- `TypeBadge`: Types de badges
- `Porte`: Portes d'accès
- `Badge`: Badges RFID
- `Personnel`: Personnel (militaire/civil)
- `MilitaireExterneInfo`: Informations spécifiques militaires externes
- `CivilInfo`: Informations spécifiques civils
- `AttributionBadge`: Attributions de badges
- `Passage`: Enregistrements de passages

#### Associations principales

```javascript
// Personnel associations
Personnel.belongsTo(TypePersonnel, { foreignKey: 'id_type_personnel', as: 'typePersonnel' });
Personnel.belongsTo(Grade, { foreignKey: 'id_grade', as: 'grade' });
Personnel.belongsTo(Unite, { foreignKey: 'id_unite', as: 'unite' });
Personnel.hasOne(MilitaireExterneInfo, { foreignKey: 'id_personnel', as: 'militaireExterneInfo' });
Personnel.hasOne(CivilInfo, { foreignKey: 'id_personnel', as: 'civilInfo' });
Personnel.hasMany(AttributionBadge, { foreignKey: 'id_personnel', as: 'attributions' });

// Badge associations
Badge.belongsTo(TypeBadge, { foreignKey: 'id_type_badge', as: 'typeBadge' });
Badge.hasMany(AttributionBadge, { foreignKey: 'id_badge', as: 'attributions' });
Badge.hasMany(Passage, { foreignKey: 'id_badge', as: 'passages' });

// Attribution associations
AttributionBadge.belongsTo(Personnel, { foreignKey: 'id_personnel', as: 'personnel' });
AttributionBadge.belongsTo(Badge, { foreignKey: 'id_badge', as: 'badge' });

// Passage associations
Passage.belongsTo(Badge, { foreignKey: 'id_badge', as: 'badge' });
Passage.belongsTo(Porte, { foreignKey: 'id_porte', as: 'porte' });
```

### Validation des données

**Fichier**: `server/utils/validation.js`

**Schémas de validation Joi**:
- `militaireInterne`: Validation militaire interne
- `militaireExterne`: Validation militaire externe
- `civil`: Validation civil externe
- `badgeCreation`: Validation création badge
- `passage`: Validation enregistrement passage

### Utilitaires

**Fichier**: `server/utils/badgeUtils.js`
- `getBadgesDisponibles()`: Récupérer badges disponibles
- `cloturerBadgeVisiteur()`: Clôturer badge visiteur
- `generateEPCCode()`: Générer code EPC unique

---

## Frontend React

### Architecture générale

**Structure des dossiers**:
```
client/src/
├── components/          # Composants réutilisables
│   ├── ui/             # Composants UI de base
│   ├── Layout.jsx      # Layout principal
│   ├── Sidebar.jsx     # Navigation latérale
│   ├── Header.jsx      # En-tête
│   └── ...
├── pages/              # Pages de l'application
├── services/           # Services API
├── contexts/           # Contextes React
├── assets/             # Ressources statiques
├── App.jsx             # Composant racine
└── main.jsx            # Point d'entrée
```

### Configuration

**Fichier**: `client/package.json`
- **Framework**: React 19 + Vite
- **Styling**: Tailwind CSS 4.1.11
- **Routing**: React Router DOM 7.6.3
- **Tables**: TanStack React Table 8.21.3
- **Charts**: Recharts 3.0.2
- **Icons**: Lucide React 0.525.0
- **HTTP**: Axios 1.10.0

### Pages principales

#### 1. Dashboard (`/dashboard`)
**Fichier**: `client/src/pages/Dashboard.jsx`

**Fonctionnalités**:
- Cartes statistiques (StatCards)
- Historique temps réel (RealTimeHistory)
- Journal des accès (AccessLogTable)
- Actions rapides (QuickActions)
- Auto-refresh toutes les 5 secondes

**Services utilisés**:
- `personnelService.getAll()`
- `passageService.getRecent(10)`
- `badgeService.getAll()`

#### 2. Gestion du Personnel (`/personnel`)
**Fichier**: `client/src/pages/PersonnelManagement.jsx`

**Fonctionnalités**:
- Liste du personnel avec filtres
- Formulaire de création (PersonnelForm)
- Actions CRUD protégées (ProtectedAction)
- Recherche et pagination

**Composants**:
- `PersonnelForm`: Formulaire de saisie
- `DataTable`: Tableau avec tri/filtres
- `Modal`: Fenêtres modales

#### 3. Gestion des Badges (`/badges`)
**Fichier**: `client/src/pages/BadgeManagement.jsx`

**Fonctionnalités**:
- Liste des badges avec statuts
- Création de badges visiteurs
- Activation/désactivation
- Historique des attributions

#### 4. Journal des Accès (`/journal`)
**Fichier**: `client/src/pages/AccessLog.jsx`

**Fonctionnalités**:
- Historique complet des passages
- Filtres avancés (date, porte, type, résultat)
- Export des données
- Recherche temps réel

#### 5. Attributions Actives (`/attributions`)
**Fichier**: `client/src/pages/ActiveAssignments.jsx`

**Fonctionnalités**:
- Liste des attributions en cours
- Clôture d'attributions
- Alertes d'expiration
- Gestion des badges visiteurs

#### 6. Statistiques (`/statistiques`)
**Fichier**: `client/src/pages/Statistics.jsx`

**Fonctionnalités**:
- Graphiques interactifs (Recharts)
- Statistiques par période
- Rapports d'utilisation
- Export de données

**Types de graphiques**:
- Graphiques en barres (passages par jour)
- Graphiques linéaires (tendances)
- Graphiques circulaires (répartition)
- Graphiques en aires (évolution)

#### 7. Paramètres (`/parametres`)
**Fichier**: `client/src/pages/Settings.jsx`

**Fonctionnalités**:
- Gestion des unités
- Gestion des grades
- Configuration des portes
- Paramètres système

### Services API

#### Configuration de base
**Fichier**: `client/src/services/api.js`
```javascript
const api = axios.create({
  baseURL: 'http://localhost:3001/api',
  timeout: 10000,
  headers: { 'Content-Type': 'application/json' }
});
```

#### Services disponibles

##### PersonnelService
**Fichier**: `client/src/services/apiService.js`
```javascript
export const personnelService = {
  async getAll(filters = {}) { /* ... */ },
  async getById(id) { /* ... */ },
  async createMilitaireInterne(data) { /* ... */ },
  async createMilitaireExterne(data) { /* ... */ },
  async createCivilExterne(data) { /* ... */ }
};
```

##### BadgeService
```javascript
export const badgeService = {
  async getAll(filters = {}) { /* ... */ },
  async getById(id) { /* ... */ },
  async getAvailable(type) { /* ... */ },
  async createVisitor(data) { /* ... */ },
  async toggleStatus(id) { /* ... */ },
  async getAttributions(filters = {}) { /* ... */ },
  async closeAttribution(id) { /* ... */ }
};
```

##### PassageService
```javascript
export const passageService = {
  async getAll(filters = {}) { /* ... */ },
  async getRecent(limit = 10) { /* ... */ },
  async getStats(period = 24) { /* ... */ },
  async verifyAccess(epcCode) { /* ... */ },
  async record(data) { /* ... */ }
};
```

##### ReferenceService
```javascript
export const referenceService = {
  async getAll() { /* ... */ },
  async getGrades() { /* ... */ },
  async getUnites() { /* ... */ },
  async getTypesPersonnel() { /* ... */ },
  async getTypesBadge() { /* ... */ },
  async getPortes() { /* ... */ }
};
```

##### StatisticsService
```javascript
export const statisticsService = {
  async getBadgeStats() { /* ... */ },
  async getAttributionHistory(limit = 20) { /* ... */ },
  async getExpiringBadges(hours = 24) { /* ... */ },
  async getUsageReport(period = 7) { /* ... */ },
  async getFullStats(dateRange) { /* ... */ }
};
```

### Adaptation des données

**Fichier**: `client/src/services/dataAdapter.js`

**Fonctions principales**:
- `adaptPersonnelToBackend()`: Adaptation personnel frontend → backend
- `adaptPersonnelFromBackend()`: Adaptation personnel backend → frontend
- `adaptBadgeToBackend()`: Adaptation badge frontend → backend
- `adaptBadgeFromBackend()`: Adaptation badge backend → frontend
- `validatePersonnelData()`: Validation données personnel
- `validateBadgeData()`: Validation données badge

**Exemple d'adaptation**:
```javascript
export const adaptPersonnelToBackend = (personnelData) => {
  const baseData = {
    nom: personnelData.nom,
    prenom: personnelData.prenom,
    id_type_personnel: getTypePersonnelId(personnelData.type)
  };

  if (personnelData.type === 'militaire_interne') {
    return {
      ...baseData,
      matricule: personnelData.matricule,
      id_grade: parseInt(personnelData.grade),
      id_unite: parseInt(personnelData.unite)
    };
  }
  // ... autres types
};
```

### Contextes React

#### AuthContext
**Fichier**: `client/src/contexts/AuthContext.jsx`

**Fonctionnalités**:
- Gestion de l'authentification
- Contrôle des permissions
- Rôles utilisateur (admin/user)
- Protection des actions

**API**:
```javascript
const { user, login, logout, hasPermission, isAdmin, isReadOnly } = useAuth();
```

### Composants UI

#### Composants de base
**Fichier**: `client/src/components/ui/index.js`

**Composants disponibles**:
- `Card`, `CardHeader`, `CardContent`, `CardTitle`: Cartes
- `Modal`: Fenêtres modales
- `DataTable`: Tableaux de données
- `Alert`: Alertes et notifications
- `Button`: Boutons avec variantes
- `Input`, `Select`, `Textarea`: Champs de formulaire
- `Badge`: Badges d'état
- `Spinner`: Indicateurs de chargement
- `ToastProvider`: Notifications toast

#### Composants métier

##### StatCards
**Fichier**: `client/src/components/StatCards.jsx`
- Affichage des statistiques principales
- Cartes avec icônes et couleurs
- Données temps réel

##### RealTimeHistory
**Fichier**: `client/src/components/RealTimeHistory.jsx`
- Historique des passages récents
- Mise à jour automatique
- Filtres par type de personnel

##### AccessLogTable
**Fichier**: `client/src/components/AccessLogTable.jsx`
- Tableau des passages avec TanStack Table
- Tri et filtres avancés
- Pagination côté client

##### PersonnelForm
**Fichier**: `client/src/components/PersonnelForm.jsx`
- Formulaire dynamique selon le type
- Validation côté client
- Adaptation des données

##### QuickActions
**Fichier**: `client/src/components/QuickActions.jsx`
- Actions rapides du dashboard
- Création rapide de personnel
- Attribution de badges

##### ProtectedAction
**Fichier**: `client/src/components/ProtectedAction.jsx`
- Protection des actions selon les permissions
- Affichage conditionnel
- Gestion des rôles

### Navigation

#### Sidebar
**Fichier**: `client/src/components/Sidebar.jsx`

**Menu principal**:
- Tableau de bord (`/dashboard`)
- Gestion du personnel (`/personnel`)
- Gestion des badges (`/badges`)
- Journal des passages (`/journal`)
- Attributions actives (`/attributions`)
- Statistiques (`/statistiques`)
- Paramètres (`/parametres`)

**Fonctionnalités**:
- Navigation active
- Icônes Lucide React
- Descriptions des sections
- Responsive design

#### Header
**Fichier**: `client/src/components/Header.jsx`
- Titre de la page courante
- Informations utilisateur
- Actions globales
- Notifications

### Styling

#### Tailwind CSS
**Configuration**: Tailwind CSS 4.1.11 avec plugin Vite
**Classes principales**:
- Layout: `flex`, `grid`, `container`
- Spacing: `p-*`, `m-*`, `gap-*`
- Colors: `bg-*`, `text-*`, `border-*`
- Typography: `text-*`, `font-*`
- States: `hover:*`, `focus:*`, `active:*`

#### Design System
**Couleurs principales**:
- Primary: Blue (système)
- Success: Green (actions réussies)
- Warning: Yellow (alertes)
- Error: Red (erreurs)
- Gray: Neutral (texte, bordures)

**Composants stylés**:
- Cards avec ombres et bordures arrondies
- Boutons avec états hover/focus
- Formulaires avec validation visuelle
- Tableaux avec tri et filtres
- Graphiques avec thème cohérent

---

## Configuration et déploiement

### Variables d'environnement

#### Backend (.env)
```env
# Base de données
DB_HOST=localhost
DB_PORT=5432
DB_NAME=controle_acces
DB_USER=postgres
DB_PASSWORD=password

# Serveur
PORT=3001
NODE_ENV=development

# Sécurité
JWT_SECRET=your_jwt_secret_key
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS
CORS_ORIGIN=http://localhost:5173
```

#### Frontend
```env
VITE_API_URL=http://localhost:3001/api
VITE_APP_TITLE=Système de Contrôle d'Accès
```

### Scripts de démarrage

#### Backend
```json
{
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "test": "jest",
    "test:watch": "jest --watch"
  }
}
```

#### Frontend
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "lint": "eslint .",
    "preview": "vite preview"
  }
}
```

### Base de données

#### Initialisation
1. Créer la base PostgreSQL
2. Exécuter `database/schema.sql`
3. Exécuter `database/init-badges.sql`
4. Optionnel: `node server/scripts/seedData.js`

#### Scripts utilitaires
- `server/scripts/seedData.js`: Données de test
- `server/scripts/resetBadgeTypes.js`: Reset types badges
- `server/scripts/seedPassages.js`: Passages de test

### Déploiement

#### Production
1. **Backend**:
   - `npm install --production`
   - Configurer variables d'environnement
   - `npm start`

2. **Frontend**:
   - `npm run build`
   - Servir le dossier `dist/`

3. **Base de données**:
   - PostgreSQL en production
   - Sauvegardes automatiques
   - Index optimisés

#### Docker (optionnel)
```dockerfile
# Backend Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

---

## Tests et validation

### Tests Backend

#### Configuration Jest
**Fichier**: `server/package.json`
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch"
  },
  "devDependencies": {
    "jest": "^29.7.0",
    "supertest": "^6.3.3"
  }
}
```

#### Tests disponibles
- Tests d'intégration API
- Tests des modèles Sequelize
- Tests des contrôleurs
- Tests des utilitaires

### Validation Frontend

#### ESLint
**Configuration**: `client/eslint.config.js`
- React hooks rules
- React refresh plugin
- Standards JavaScript

#### Tests de données
**Fichier**: `client/src/test-data-adapter.js`
- Tests d'adaptation des données
- Validation des schémas
- Tests de transformation

### Validation des données

#### Côté Backend (Joi)
```javascript
const schemas = {
  militaireInterne: Joi.object({
    nom: Joi.string().required(),
    prenom: Joi.string().required(),
    matricule: Joi.string().required(),
    id_grade: Joi.number().integer().required(),
    id_unite: Joi.number().integer().required()
  })
};
```

#### Côté Frontend
```javascript
export const validatePersonnelData = (data) => {
  const errors = [];

  if (!data.nom?.trim()) errors.push('Le nom est requis');
  if (!data.prenom?.trim()) errors.push('Le prénom est requis');

  return {
    isValid: errors.length === 0,
    errors
  };
};
```

### Monitoring et logs

#### Backend
- Morgan pour les logs HTTP
- Console.error pour les erreurs
- Logs structurés en production

#### Frontend
- Console.warn pour les avertissements
- Error boundaries React
- Gestion des erreurs API

---

## Sécurité et bonnes pratiques

### Sécurité Backend
- Helmet.js pour les en-têtes sécurisés
- Rate limiting configurable
- Validation stricte des entrées
- CORS configuré
- JWT pour l'authentification (prévu)

### Sécurité Frontend
- Validation côté client
- Échappement des données
- Protection CSRF
- Gestion sécurisée des tokens

### Performance
- Index de base de données optimisés
- Pagination côté serveur
- Compression gzip
- Cache des données de référence
- Lazy loading des composants

### Maintenance
- Code modulaire et réutilisable
- Documentation inline
- Tests automatisés
- Logs structurés
- Monitoring des erreurs

---

*Documentation générée le 2025-01-09*
*Version du système: 1.0.0*
