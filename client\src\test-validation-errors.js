// Script de test pour vérifier la validation des erreurs
import {
  validatePersonnelData,
  validateBadgeData
} from './services/dataAdapter.js';

console.log('=== TEST DE VALIDATION DES ERREURS ===\n');

// Test 1: Militaire interne avec données manquantes
console.log('1. Test Militaire Interne - Données manquantes:');
const militaireInterneInvalide = {
  type: 'militaire_interne',
  nom: '', // Nom vide
  prenom: 'Jean',
  // matricule manquant
  grade: '', // Grade vide
  unite: 'invalid' // ID invalide
};

const validation1 = validatePersonnelData(militaireInterneInvalide);
console.log('Validation:', validation1);
console.log('Erreurs:', validation1.errors);

console.log('\n---\n');

// Test 2: Militaire externe avec ID invalides
console.log('2. Test Militaire Externe - ID invalides:');
const militaireExterneInvalide = {
  type: 'militaire_externe',
  nom: '<PERSON>',
  prenom: '<PERSON>',
  matricule: 'ME67890',
  cin: '12345678',
  grade: '-1', // ID négatif
  unite_origine: 'abc', // ID non numérique
  destination: '0', // ID zéro
  heure_entree: 'invalid-date', // Date invalide
  objet_visite: 'Test',
  badge_id: '' // Badge manquant
};

const validation2 = validatePersonnelData(militaireExterneInvalide);
console.log('Validation:', validation2);
console.log('Erreurs:', validation2.errors);

console.log('\n---\n');

// Test 3: Civil externe avec champs manquants
console.log('3. Test Civil Externe - Champs manquants:');
const civilExterneInvalide = {
  type: 'civil_externe',
  nom: 'Durand',
  prenom: 'Marie',
  // cin manquant
  societe: 'TechCorp',
  // destination manquante
  // heure_entree manquante
  objet_visite: 'Test',
  // badge_id manquant
};

const validation3 = validatePersonnelData(civilExterneInvalide);
console.log('Validation:', validation3);
console.log('Erreurs:', validation3.errors);

console.log('\n---\n');

// Test 4: Badge avec type invalide (création)
console.log('4. Test Badge Création - Type invalide:');
const badgeCreationInvalide = {
  type: 'badge_invalide' // Type non autorisé
};

const validation4 = validateBadgeData(badgeCreationInvalide, true);
console.log('Validation:', validation4);
console.log('Erreurs:', validation4.errors);

console.log('\n---\n');

// Test 5: Badge avec données invalides (mise à jour)
console.log('5. Test Badge Mise à jour - Données invalides:');
const badgeUpdateInvalide = {
  numero: '', // Numéro vide
  type: 'invalid', // Type invalide
  statut: 'statut_invalide', // Statut invalide
  permanent: false,
  description: 'Test'
};

const validation5 = validateBadgeData(badgeUpdateInvalide, false);
console.log('Validation:', validation5);
console.log('Erreurs:', validation5.errors);

console.log('\n=== FIN DES TESTS DE VALIDATION ===');
