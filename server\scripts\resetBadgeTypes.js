const { sequelize } = require('../config/database');
const { TypeBadge, Badge } = require('../models');

async function resetBadgeTypes() {
  try {
    console.log('🔄 Réinitialisation des types de badges...');

    // Supprimer les anciens types de badges
    await TypeBadge.destroy({ where: {} });
    console.log('✅ Anciens types de badges supprimés');

    // Créer les nouveaux types de badges
    const typesBadge = await TypeBadge.bulkCreate([
      { nom_type_badge: 'badge_militaire_interne', description: 'Badge permanent pour militaires internes' },
      { nom_type_badge: 'badge_visiteur_militaire', description: 'Badge temporaire pour militaires externes' },
      { nom_type_badge: 'badge_visiteur_civil', description: 'Badge temporaire pour civils externes' }
    ]);
    console.log('✅ Nouveaux types de badges créés:', typesBadge.map(t => t.nom_type_badge));

    // Supprimer les anciens badges
    await Badge.destroy({ where: {} });
    console.log('✅ Anciens badges supprimés');

    // Créer de nouveaux badges avec les bons types
    const badges = await Badge.bulkCreate([
      // Badges permanents pour militaires internes
      { epc_code: 'EPC001', id_type_badge: 1, permanent: true, actif: true },
      { epc_code: 'EPC002', id_type_badge: 1, permanent: true, actif: true },
      { epc_code: 'EPC003', id_type_badge: 1, permanent: true, actif: true },
      // Badges visiteurs militaires (disponibles)
      { epc_code: 'EPC101', id_type_badge: 2, permanent: false, actif: true },
      { epc_code: 'EPC102', id_type_badge: 2, permanent: false, actif: true },
      { epc_code: 'EPC103', id_type_badge: 2, permanent: false, actif: true },
      { epc_code: 'EPC104', id_type_badge: 2, permanent: false, actif: true },
      // Badges visiteurs civils (disponibles)
      { epc_code: 'EPC201', id_type_badge: 3, permanent: false, actif: true },
      { epc_code: 'EPC202', id_type_badge: 3, permanent: false, actif: true },
      { epc_code: 'EPC203', id_type_badge: 3, permanent: false, actif: true },
      { epc_code: 'EPC204', id_type_badge: 3, permanent: false, actif: true }
    ]);
    console.log('✅ Nouveaux badges créés:', badges.length);

    // Vérifier les badges créés
    const badgesWithTypes = await Badge.findAll({
      include: [{ model: TypeBadge, as: 'typeBadge' }]
    });

    console.log('\n📋 Badges créés:');
    badgesWithTypes.forEach(badge => {
      console.log(`- ${badge.epc_code}: ${badge.typeBadge.nom_type_badge} (${badge.permanent ? 'permanent' : 'temporaire'})`);
    });

    console.log('\n🎉 Réinitialisation terminée avec succès!');
  } catch (error) {
    console.error('❌ Erreur lors de la réinitialisation:', error);
  } finally {
    await sequelize.close();
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  resetBadgeTypes();
}

module.exports = resetBadgeTypes;
