// Script de test pour vérifier les adaptations de données
import {
  adaptPersonnelToBackend,
  adaptBadgeToBackend,
  validatePersonnelData,
  validateBadgeData
} from './services/dataAdapter.js';

// Test des données de personnel
console.log('=== TEST DES ADAPTATIONS DE DONNÉES ===\n');

// Test 1: Militaire interne
console.log('1. Test Militaire Interne:');
const militaireInterneData = {
  type: 'militaire_interne',
  nom: 'Dupont',
  prenom: '<PERSON>',
  matricule: 'M12345',
  grade: '5', // ID du grade Lieutenant
  unite: '2'  // ID de l'unité
};

console.log('Données frontend:', militaireInterneData);
const validation1 = validatePersonnelData(militaireInterneData);
console.log('Validation:', validation1);

if (validation1.isValid) {
  const adapted1 = adaptPersonnelToBackend(militaireInterneData);
  console.log('Données adaptées pour backend:', adapted1);
} else {
  console.log('❌ Validation échouée');
}

console.log('\n---\n');

// Test 2: Militaire externe
console.log('2. Test Militaire Externe:');
const militaireExterneData = {
  type: 'militaire_externe',
  nom: 'Martin',
  prenom: 'Pierre',
  matricule: 'ME67890',
  cin: '12345678',
  grade: '3', // ID du grade Sergent
  unite_origine: '1', // ID de l'unité d'origine
  destination: '2', // ID de l'unité de destination
  heure_entree: '2024-01-15T08:00:00',
  objet_visite: 'Réunion de coordination',
  badge_id: '10' // ID du badge visiteur
};

console.log('Données frontend:', militaireExterneData);
const validation2 = validatePersonnelData(militaireExterneData);
console.log('Validation:', validation2);

if (validation2.isValid) {
  const adapted2 = adaptPersonnelToBackend(militaireExterneData);
  console.log('Données adaptées pour backend:', adapted2);
} else {
  console.log('❌ Validation échouée');
}

console.log('\n---\n');

// Test 3: Civil externe
console.log('3. Test Civil Externe:');
const civilExterneData = {
  type: 'civil_externe',
  nom: 'Durand',
  prenom: 'Marie',
  cin: '87654321',
  societe: 'TechCorp',
  destination: '3', // ID de l'unité de destination
  heure_entree: '2024-01-15T09:30:00',
  objet_visite: 'Maintenance informatique',
  badge_id: '15' // ID du badge visiteur civil
};

console.log('Données frontend:', civilExterneData);
const validation3 = validatePersonnelData(civilExterneData);
console.log('Validation:', validation3);

if (validation3.isValid) {
  const adapted3 = adaptPersonnelToBackend(civilExterneData);
  console.log('Données adaptées pour backend:', adapted3);
} else {
  console.log('❌ Validation échouée');
}

console.log('\n---\n');

// Test 4: Badge visiteur (création)
console.log('4. Test Badge Visiteur (Création):');
const badgeCreationData = {
  type: 'badge_visiteur_militaire'
};

console.log('Données frontend:', badgeCreationData);
const validation4 = validateBadgeData(badgeCreationData, true);
console.log('Validation:', validation4);

if (validation4.isValid) {
  const adapted4 = adaptBadgeToBackend(badgeCreationData, true);
  console.log('Données adaptées pour backend:', adapted4);
} else {
  console.log('❌ Validation échouée');
}

console.log('\n---\n');

// Test 5: Badge (mise à jour)
console.log('5. Test Badge (Mise à jour):');
const badgeUpdateData = {
  numero: 'V001',
  type: '2', // ID du type de badge
  statut: 'actif',
  permanent: false,
  description: 'Badge visiteur militaire'
};

console.log('Données frontend:', badgeUpdateData);
const validation5 = validateBadgeData(badgeUpdateData, false);
console.log('Validation:', validation5);

if (validation5.isValid) {
  const adapted5 = adaptBadgeToBackend(badgeUpdateData, false);
  console.log('Données adaptées pour backend:', adapted5);
} else {
  console.log('❌ Validation échouée');
}

console.log('\n=== FIN DES TESTS ===');
