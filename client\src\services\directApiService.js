import api from './api';

// Service API direct sans adapters - remplace apiService.js
// Utilise les nouvelles routes spécifiques pour chaque cas d'usage

// ===== PERSONNEL SERVICE =====
export const personnelService = {
  // Pour le dashboard - données simplifiées
  async getDashboardSummary() {
    const response = await api.get('/personnel/dashboard/summary');
    return response.data;
  },

  // Pour la gestion - données complètes avec pagination
  async getManagementList(filters = {}) {
    const response = await api.get('/personnel/management/list', { params: filters });
    return response.data;
  },

  // Obtenir un personnel par ID (garde la route existante)
  async getById(id) {
    const response = await api.get(`/personnel/${id}`);
    return response.data;
  },

  // Créer un militaire interne (garde la route existante)
  async createMilitaireInterne(data) {
    const response = await api.post('/personnel/internes', data);
    return response.data;
  },

  // Créer un militaire externe (garde la route existante)
  async createMilitaireExterne(data) {
    const response = await api.post('/personnel/militaires-externes', data);
    return response.data;
  },

  // Créer un civil externe (garde la route existante)
  async createCivilExterne(data) {
    const response = await api.post('/personnel/civils-externes', data);
    return response.data;
  },

  // Méthode générique pour créer selon le type
  async create(data) {
    switch (data.type) {
      case 'militaire_interne':
        return this.createMilitaireInterne(data);
      case 'militaire_externe':
        return this.createMilitaireExterne(data);
      case 'civil_externe':
        return this.createCivilExterne(data);
      default:
        throw new Error('Type de personnel non supporté');
    }
  }
};

// ===== BADGE SERVICE =====
export const badgeService = {
  // Pour le dashboard - statistiques simplifiées
  async getDashboardStats() {
    const response = await api.get('/badges/dashboard/stats');
    return response.data;
  },

  // Pour la gestion - liste complète avec pagination
  async getManagementList(filters = {}) {
    const response = await api.get('/badges/management/list', { params: filters });
    return response.data;
  },

  // Pour les formulaires - badges visiteurs disponibles
  async getAvailableVisitorBadges() {
    const response = await api.get('/badges/form/available-visitors');
    return response.data;
  },

  // Pour les attributions actives
  async getActiveAssignments(filters = {}) {
    const response = await api.get('/badges/assignments/active', { params: filters });
    return response.data;
  },

  // Obtenir un badge par ID (garde la route existante)
  async getById(id) {
    const response = await api.get(`/badges/${id}`);
    return response.data;
  },

  // Créer un badge visiteur (garde la route existante)
  async createVisitor(data) {
    const response = await api.post('/badges/visiteurs', data);
    return response.data;
  },

  // Activer/Désactiver un badge (garde la route existante)
  async toggleStatus(id) {
    const response = await api.put(`/badges/${id}/toggle-statut`);
    return response.data;
  },

  // Clôturer une attribution (garde la route existante)
  async closeAttribution(id) {
    const response = await api.put(`/badges/attributions/${id}/cloturer`);
    return response.data;
  }
};

// ===== PASSAGE SERVICE =====
export const passageService = {
  // Pour le dashboard - passages récents
  async getDashboardRecent(limit = 10) {
    const response = await api.get('/passages/dashboard/recent', { params: { limit } });
    return response.data;
  },

  // Pour le journal - historique complet avec filtres
  async getLogHistory(filters = {}) {
    const response = await api.get('/passages/log/history', { params: filters });
    return response.data;
  },

  // Obtenir les statistiques (garde la route existante)
  async getStatistics(periode = 24) {
    const response = await api.get('/passages/statistiques', { params: { periode } });
    return response.data;
  },

  // Vérifier l'accès d'un badge (garde la route existante)
  async verifyAccess(epcCode) {
    const response = await api.get(`/passages/verifier/${epcCode}`);
    return response.data;
  },

  // Enregistrer un passage (garde la route existante)
  async create(data) {
    const response = await api.post('/passages', data);
    return response.data;
  }
};

// ===== REFERENCE SERVICE =====
export const referenceService = {
  // Pour les formulaires - données formatées
  async getFormData() {
    const response = await api.get('/reference/form/data');
    return response.data;
  },

  // Obtenir toutes les références (garde la route existante)
  async getAll() {
    const response = await api.get('/reference/all');
    return response.data;
  },

  // Méthodes spécifiques (gardent les routes existantes)
  async getGrades() {
    const response = await api.get('/reference/grades');
    return response.data;
  },

  async getUnites() {
    const response = await api.get('/reference/unites');
    return response.data;
  },

  async getTypesPersonnel() {
    const response = await api.get('/reference/types-personnel');
    return response.data;
  },

  async getTypesBadge() {
    const response = await api.get('/reference/types-badge');
    return response.data;
  },

  async getPortes() {
    const response = await api.get('/reference/portes');
    return response.data;
  }
};

// ===== STATISTICS SERVICE =====
export const statisticsService = {
  // Obtenir les statistiques des badges (garde la route existante)
  async getBadgeStats() {
    try {
      const response = await api.get('/statistiques/badges');
      return response.data;
    } catch (error) {
      console.warn('Erreur dans getBadgeStats:', error);
      return {
        totalBadges: 0,
        badgesActifs: 0,
        badgesPermanents: 0,
        badgesMilitairesInternes: 0,
        badgesVisiteursMilitaires: 0,
        badgesVisiteursCivils: 0,
        attributionsActives: 0
      };
    }
  },

  // Obtenir l'historique des attributions (garde la route existante)
  async getAttributionHistory(limit = 20) {
    const response = await api.get('/statistiques/historique-attributions', {
      params: { limit }
    });
    return response.data;
  },

  // Obtenir les badges expirants (garde la route existante)
  async getExpiringBadges(hours = 24) {
    const response = await api.get('/statistiques/badges-expirants', {
      params: { heures: hours }
    });
    return response.data;
  },

  // Obtenir le rapport d'utilisation (garde la route existante)
  async getUsageReport(period = 7) {
    const response = await api.get('/statistiques/rapport-utilisation', {
      params: { periode: period }
    });
    return response.data;
  },

  // Méthode pour obtenir toutes les statistiques pour la page Statistics
  async getFullStats(dateRange = {}) {
    try {
      const [badgeStats, passageStats] = await Promise.all([
        this.getBadgeStats(),
        passageService.getStatistics(24)
      ]);

      return {
        badges: badgeStats,
        passages: passageStats,
        // Données par défaut pour les graphiques
        passagesParJour: [],
        passagesParHeure: [],
        passagesParPorte: [],
        passagesParType: [],
        tendances: {},
        resume: {
          totalPassages: passageStats.totalPassages || 0,
          passagesAutorises: passageStats.passagesAutorises || 0,
          tauxAutorisation: passageStats.tauxAutorisation || 0,
          heurePointe: '--:--',
          passagesHeurePointe: 0,
          badgesActifs: badgeStats.badgesActifs || 0,
          badgesVisiteurs: badgeStats.badgesVisiteursMilitaires + badgeStats.badgesVisiteursCivils || 0
        }
      };
    } catch (error) {
      console.warn('Erreur dans getFullStats:', error);
      return {
        badges: {},
        passages: {},
        passagesParJour: [],
        passagesParHeure: [],
        passagesParPorte: [],
        passagesParType: [],
        tendances: {},
        resume: {
          totalPassages: 0,
          passagesAutorises: 0,
          tauxAutorisation: 0,
          heurePointe: '--:--',
          passagesHeurePointe: 0,
          badgesActifs: 0,
          badgesVisiteurs: 0
        }
      };
    }
  }
};

// Export par défaut pour compatibilité
export default {
  personnelService,
  badgeService,
  passageService,
  referenceService,
  statisticsService
};
