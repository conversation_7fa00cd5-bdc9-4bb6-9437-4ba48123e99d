const express = require('express');
const router = express.Router();
const {
  obtenirGrades,
  obtenirUnites,
  obtenirTypesPersonnel,
  obtenirTypesBadge,
  obtenirPortes,
  obtenirToutesReferences
} = require('../controllers/referenceController');

/**
 * @route GET /api/reference/grades
 * @desc Récupérer tous les grades militaires
 * @access Public
 */
router.get('/grades', obtenirGrades);

/**
 * @route GET /api/reference/unites
 * @desc Récupérer toutes les unités
 * @access Public
 */
router.get('/unites', obtenirUnites);

/**
 * @route GET /api/reference/types-personnel
 * @desc Récupérer tous les types de personnel
 * @access Public
 */
router.get('/types-personnel', obtenirTypesPersonnel);

/**
 * @route GET /api/reference/types-badge
 * @desc Récupérer tous les types de badge
 * @access Public
 */
router.get('/types-badge', obtenirTypesBadge);

/**
 * @route GET /api/reference/portes
 * @desc Récupérer toutes les portes actives
 * @access Public
 */
router.get('/portes', obtenirPortes);

/**
 * @route GET /api/reference/all
 * @desc Récupérer toutes les données de référence
 * @access Public
 */
router.get('/all', obtenirToutesReferences);

/**
 * @route GET /api/reference/form/data
 * @desc Récupérer les données de référence formatées pour les formulaires
 * @access Public
 * @returns Données formatées pour les selects des formulaires
 */
router.get('/form/data', async (req, res) => {
  try {
    const { Grade, Unite, TypePersonnel, TypeBadge, Porte } = require('../models');

    const [grades, unites, typesPersonnel, typesBadge, portes] = await Promise.all([
      Grade.findAll({
        attributes: ['id', 'nom_grade', 'niveau'],
        order: [['niveau', 'ASC']]
      }),
      Unite.findAll({
        attributes: ['id', 'nom_unite', 'code_unite'],
        order: [['nom_unite', 'ASC']]
      }),
      TypePersonnel.findAll({
        attributes: ['id', 'nom_type', 'description'],
        order: [['nom_type', 'ASC']]
      }),
      TypeBadge.findAll({
        attributes: ['id', 'nom_type_badge', 'description'],
        order: [['nom_type_badge', 'ASC']]
      }),
      Porte.findAll({
        where: { actif: true },
        attributes: ['id', 'libelle', 'description'],
        order: [['libelle', 'ASC']]
      })
    ]);

    res.json({
      grades: grades.map(g => ({
        id: g.id,
        nom: g.nom_grade,
        niveau: g.niveau,
        label: `${g.nom_grade} (Niveau ${g.niveau})`
      })),
      unites: unites.map(u => ({
        id: u.id,
        nom: u.nom_unite,
        code: u.code_unite,
        label: u.code_unite ? `${u.nom_unite} (${u.code_unite})` : u.nom_unite
      })),
      typesPersonnel: typesPersonnel.map(t => ({
        id: t.id,
        nom: t.nom_type,
        description: t.description
      })),
      typesBadge: typesBadge.map(t => ({
        id: t.id,
        nom: t.nom_type_badge,
        description: t.description
      })),
      portes: portes.map(p => ({
        id: p.id,
        nom: p.libelle,
        description: p.description
      }))
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des données de référence pour formulaires:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des données de référence',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
