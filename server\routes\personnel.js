const express = require('express');
const router = express.Router();
const { validationMiddleware, schemas } = require('../utils/validation');
const {
  creerMilitaireInterne,
  creerMilitaireExterne,
  creerCivilExterne,
  listerPersonnel,
  obtenirPersonnel
} = require('../controllers/personnelController');

/**
 * @route POST /api/personnel/internes
 * @desc Créer un militaire interne avec badge permanent automatique
 * @access Public (à sécuriser plus tard)
 */
router.post('/internes', 
  validationMiddleware(schemas.militaireInterne),
  creerMilitaireInterne
);

/**
 * @route POST /api/personnel/militaires-externes
 * @desc Créer un militaire externe avec badge visiteur manuel
 * @access Public (à sécuriser plus tard)
 */
router.post('/militaires-externes', 
  validationMiddleware(schemas.militaireExterne),
  creerMilitaireExterne
);

/**
 * @route POST /api/personnel/civils-externes
 * @desc Créer un civil externe avec badge visiteur manuel
 * @access Public (à sécuriser plus tard)
 */
router.post('/civils-externes', 
  validationMiddleware(schemas.civil),
  creerCivilExterne
);

/**
 * @route GET /api/personnel
 * @desc Lister le personnel avec filtres et pagination
 * @access Public (à sécuriser plus tard)
 * @query type - Type de personnel (militaire_interne, militaire_externe, civil_externe)
 * @query page - Numéro de page (défaut: 1)
 * @query limit - Nombre d'éléments par page (défaut: 10)
 * @query search - Recherche textuelle dans nom, prénom, matricule, CIN
 */
router.get('/', listerPersonnel);

/**
 * @route GET /api/personnel/:id
 * @desc Récupérer un personnel par ID
 * @access Public (à sécuriser plus tard)
 */
router.get('/:id', obtenirPersonnel);

/**
 * @route GET /api/personnel/dashboard/summary
 * @desc Récupérer un résumé du personnel pour le dashboard
 * @access Public (à sécuriser plus tard)
 * @returns Liste simplifiée avec type, nom, prénom pour calculs d'effectifs
 */
router.get('/dashboard/summary', async (req, res) => {
  try {
    const { Personnel, TypePersonnel } = require('../models');

    const personnel = await Personnel.findAll({
      attributes: ['id', 'nom', 'prenom'],
      include: [
        {
          model: TypePersonnel,
          as: 'typePersonnel',
          attributes: ['nom_type']
        }
      ]
    });

    const result = personnel.map(p => ({
      id: p.id,
      nom: p.nom,
      prenom: p.prenom,
      type: p.typePersonnel.nom_type
    }));

    res.json(result);
  } catch (error) {
    console.error('Erreur lors de la récupération du résumé personnel:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération du résumé personnel',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route GET /api/personnel/management/list
 * @desc Récupérer la liste complète du personnel pour la gestion
 * @access Public (à sécuriser plus tard)
 * @returns Liste complète avec tous les détails pour la gestion
 */
router.get('/management/list', async (req, res) => {
  try {
    const { type, page = 1, limit = 10, search } = req.query;
    const offset = (page - 1) * limit;

    const { Personnel, TypePersonnel, Grade, Unite, AttributionBadge, Badge } = require('../models');

    let whereClause = {};
    let includeClause = [
      {
        model: TypePersonnel,
        as: 'typePersonnel',
        attributes: ['nom_type']
      },
      {
        model: Grade,
        as: 'grade',
        attributes: ['nom_grade', 'niveau'],
        required: false
      },
      {
        model: Unite,
        as: 'unite',
        attributes: ['nom_unite', 'code_unite'],
        required: false
      },
      {
        model: AttributionBadge,
        as: 'attributions',
        include: [{
          model: Badge,
          as: 'badge',
          attributes: ['epc_code', 'actif']
        }],
        where: { statut: 'actif' },
        required: false
      }
    ];

    // Filtre par type de personnel
    if (type && type !== 'all') {
      includeClause[0].where = { nom_type: type };
    }

    // Recherche textuelle
    if (search) {
      const { Op } = require('sequelize');
      whereClause = {
        [Op.or]: [
          { nom: { [Op.iLike]: `%${search}%` } },
          { prenom: { [Op.iLike]: `%${search}%` } },
          { matricule: { [Op.iLike]: `%${search}%` } },
          { cin: { [Op.iLike]: `%${search}%` } }
        ]
      };
    }

    const { count, rows } = await Personnel.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    const result = rows.map(p => ({
      id: p.id,
      nom: p.nom,
      prenom: p.prenom,
      matricule: p.matricule,
      cin: p.cin,
      type: p.typePersonnel.nom_type,
      grade: p.grade ? {
        nom: p.grade.nom_grade,
        niveau: p.grade.niveau
      } : null,
      unite: p.unite ? {
        nom: p.unite.nom_unite,
        code: p.unite.code_unite
      } : null,
      badge_actif: p.attributions && p.attributions.length > 0 ? {
        numero: p.attributions[0].badge.epc_code,
        actif: p.attributions[0].badge.actif
      } : null,
      date_creation: p.created_at
    }));

    res.json({
      personnel: result,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de la liste personnel:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération de la liste personnel',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
