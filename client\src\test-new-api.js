// Script de test pour valider les nouvelles routes API directes
// Exécuter avec: node client/src/test-new-api.js

import { 
  personnelService, 
  badgeService, 
  passageService, 
  referenceService, 
  statisticsService 
} from './services/directApiService.js';

const API_BASE_URL = 'http://localhost:3001/api';

// Fonction utilitaire pour tester une route
async function testRoute(routeName, testFunction) {
  console.log(`\n🧪 Test: ${routeName}`);
  console.log('─'.repeat(50));
  
  try {
    const startTime = Date.now();
    const result = await testFunction();
    const duration = Date.now() - startTime;
    
    console.log(`✅ Succès (${duration}ms)`);
    console.log(`📊 Données reçues:`, JSON.stringify(result, null, 2).substring(0, 200) + '...');
    return true;
  } catch (error) {
    console.log(`❌ Erreur: ${error.message}`);
    if (error.response) {
      console.log(`📡 Status: ${error.response.status}`);
      console.log(`📝 Réponse:`, error.response.data);
    }
    return false;
  }
}

// Tests des nouvelles routes
async function runTests() {
  console.log('🚀 Test des nouvelles routes API directes');
  console.log('=' .repeat(60));
  
  const results = [];

  // Tests Personnel Service
  results.push(await testRoute('Personnel Dashboard Summary', async () => {
    return await personnelService.getDashboardSummary();
  }));

  results.push(await testRoute('Personnel Management List', async () => {
    return await personnelService.getManagementList({ page: 1, limit: 5 });
  }));

  // Tests Badge Service
  results.push(await testRoute('Badge Dashboard Stats', async () => {
    return await badgeService.getDashboardStats();
  }));

  results.push(await testRoute('Badge Management List', async () => {
    return await badgeService.getManagementList({ page: 1, limit: 5 });
  }));

  results.push(await testRoute('Available Visitor Badges', async () => {
    return await badgeService.getAvailableVisitorBadges();
  }));

  results.push(await testRoute('Active Assignments', async () => {
    return await badgeService.getActiveAssignments({ page: 1, limit: 5 });
  }));

  // Tests Passage Service
  results.push(await testRoute('Passage Dashboard Recent', async () => {
    return await passageService.getDashboardRecent(5);
  }));

  results.push(await testRoute('Passage Log History', async () => {
    return await passageService.getLogHistory({ page: 1, limit: 5 });
  }));

  // Tests Reference Service
  results.push(await testRoute('Reference Form Data', async () => {
    return await referenceService.getFormData();
  }));

  // Tests Statistics Service
  results.push(await testRoute('Badge Statistics', async () => {
    return await statisticsService.getBadgeStats();
  }));

  results.push(await testRoute('Full Statistics', async () => {
    return await statisticsService.getFullStats();
  }));

  // Résumé des tests
  console.log('\n📋 RÉSUMÉ DES TESTS');
  console.log('=' .repeat(60));
  
  const successCount = results.filter(r => r).length;
  const totalCount = results.length;
  
  console.log(`✅ Tests réussis: ${successCount}/${totalCount}`);
  console.log(`❌ Tests échoués: ${totalCount - successCount}/${totalCount}`);
  
  if (successCount === totalCount) {
    console.log('\n🎉 Tous les tests sont passés ! La migration est réussie.');
  } else {
    console.log('\n⚠️  Certains tests ont échoué. Vérifiez que le serveur est démarré.');
  }

  return successCount === totalCount;
}

// Test de comparaison des performances
async function performanceTest() {
  console.log('\n⚡ TEST DE PERFORMANCE');
  console.log('=' .repeat(60));
  
  const iterations = 5;
  const tests = [
    {
      name: 'Dashboard Summary',
      fn: () => personnelService.getDashboardSummary()
    },
    {
      name: 'Badge Stats',
      fn: () => badgeService.getDashboardStats()
    },
    {
      name: 'Recent Passages',
      fn: () => passageService.getDashboardRecent(10)
    }
  ];

  for (const test of tests) {
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
      const start = Date.now();
      try {
        await test.fn();
        times.push(Date.now() - start);
      } catch (error) {
        console.log(`❌ Erreur dans ${test.name}: ${error.message}`);
        break;
      }
    }
    
    if (times.length === iterations) {
      const avg = times.reduce((a, b) => a + b, 0) / times.length;
      const min = Math.min(...times);
      const max = Math.max(...times);
      
      console.log(`📊 ${test.name}:`);
      console.log(`   Moyenne: ${avg.toFixed(2)}ms`);
      console.log(`   Min: ${min}ms | Max: ${max}ms`);
    }
  }
}

// Test de validation des données
async function dataValidationTest() {
  console.log('\n🔍 TEST DE VALIDATION DES DONNÉES');
  console.log('=' .repeat(60));
  
  try {
    // Test structure des données personnel
    const personnelData = await personnelService.getDashboardSummary();
    console.log('✅ Personnel Summary - Structure validée');
    
    if (Array.isArray(personnelData)) {
      const sample = personnelData[0];
      if (sample && sample.id && sample.nom && sample.prenom && sample.type) {
        console.log('✅ Personnel Summary - Champs requis présents');
      } else {
        console.log('❌ Personnel Summary - Champs manquants');
      }
    }

    // Test structure des données badges
    const badgeStats = await badgeService.getDashboardStats();
    console.log('✅ Badge Stats - Structure validée');
    
    if (typeof badgeStats.total === 'number' && 
        typeof badgeStats.actifs === 'number' && 
        typeof badgeStats.attribues === 'number') {
      console.log('✅ Badge Stats - Types de données corrects');
    } else {
      console.log('❌ Badge Stats - Types de données incorrects');
    }

    // Test structure des données de référence
    const formData = await referenceService.getFormData();
    console.log('✅ Form Data - Structure validée');
    
    if (formData.grades && formData.unites && formData.typesPersonnel) {
      console.log('✅ Form Data - Toutes les références présentes');
    } else {
      console.log('❌ Form Data - Références manquantes');
    }

  } catch (error) {
    console.log(`❌ Erreur de validation: ${error.message}`);
  }
}

// Exécution des tests
async function main() {
  console.log('🔧 Vérification de la connexion au serveur...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/reference/all`);
    if (!response.ok) {
      throw new Error(`Serveur non accessible: ${response.status}`);
    }
    console.log('✅ Serveur accessible');
  } catch (error) {
    console.log('❌ Serveur non accessible. Assurez-vous que le serveur backend est démarré.');
    console.log('   Commande: cd server && npm run dev');
    return;
  }

  // Exécution de tous les tests
  await runTests();
  await performanceTest();
  await dataValidationTest();
  
  console.log('\n🏁 Tests terminés !');
}

// Exécution si le script est appelé directement
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { runTests, performanceTest, dataValidationTest };
