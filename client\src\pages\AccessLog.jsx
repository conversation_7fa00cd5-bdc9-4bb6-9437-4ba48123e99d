import { useState, useEffect, useCallback } from 'react';
import { Calendar, Clock, Filter, Download, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { passageService, personnelService, referenceService } from '../services/directApiService';
import { Card, CardHeader, CardContent, CardTitle, DataTable, Alert } from '../components/ui';

function AccessLog() {
  const [passages, setPassages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    dateDebut: '',
    dateFin: '',
    porte: '',
    personnel: '',
    resultat: 'all'
  });
  const [portes, setPortes] = useState([]);
  const [personnels, setPersonnels] = useState([]);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      const [passagesResponse, formData, personnelsData] = await Promise.all([
        passageService.getLogHistory(filters),
        referenceService.getFormData(),
        personnelService.getDashboardSummary()
      ]);
      setPassages(passagesResponse.passages || []);
      setPortes(formData.portes || []);
      setPersonnels(personnelsData);
      setError(null);
    } catch {
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchData();

    // Auto-refresh toutes les 5 secondes
    const interval = setInterval(fetchData, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // Refetch when filters change
    fetchData();
  }, [filters, fetchData]);

  const handleFilterChange = (name, value) => {
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleExport = async () => {
    try {
      // Pour l'instant, on exporte les données actuelles en CSV
      const csvContent = "data:text/csv;charset=utf-8,"
        + "Date,Heure,Personnel,Badge,Porte,Résultat,Motif\n"
        + passages.map(passage => [
          new Date(passage.date_passage).toLocaleDateString('fr-FR'),
          new Date(passage.date_passage).toLocaleTimeString('fr-FR'),
          `${passage.personnel?.nom} ${passage.personnel?.prenom}`,
          passage.badge_numero,
          passage.porte_nom,
          passage.resultat,
          passage.motif_refus || ''
        ].join(",")).join("\n");

      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', `journal_passages_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch {
      setError('Erreur lors de l\'export');
    }
  };

  const columns = [
    {
      header: 'Date/Heure',
      accessor: 'date_passage',
      render: (passage) => (
        <div>
          <div className="font-medium text-gray-900">
            {new Date(passage.date_passage).toLocaleDateString('fr-FR')}
          </div>
          <div className="text-sm text-gray-500">
            {new Date(passage.date_passage).toLocaleTimeString('fr-FR')}
          </div>
        </div>
      )
    },
    {
      header: 'Personnel',
      render: (passage) => (
        <div>
          <div className="font-medium text-gray-900">
            {passage.personnel?.nom} {passage.personnel?.prenom}
          </div>
          <div className="text-sm text-gray-500">
            {passage.personnel?.matricule || passage.personnel?.cin}
          </div>
        </div>
      )
    },
    {
      header: 'Badge',
      accessor: 'badge_numero',
      render: (passage) => (
        <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
          #{passage.badge_numero}
        </span>
      )
    },
    {
      header: 'Porte',
      accessor: 'porte_nom',
      render: (passage) => (
        <div>
          <div className="font-medium text-gray-900">{passage.porte_nom}</div>
          <div className="text-sm text-gray-500">{passage.type_acces}</div>
        </div>
      )
    },
    {
      header: 'Résultat',
      accessor: 'resultat',
      render: (passage) => {
        const resultConfig = {
          autorise: {
            icon: CheckCircle,
            color: 'text-green-600',
            bg: 'bg-green-100',
            label: 'Autorisé'
          },
          refuse: {
            icon: XCircle,
            color: 'text-red-600',
            bg: 'bg-red-100',
            label: 'Refusé'
          },
          alerte: {
            icon: AlertCircle,
            color: 'text-orange-600',
            bg: 'bg-orange-100',
            label: 'Alerte'
          }
        };

        const config = resultConfig[passage.resultat] || resultConfig.refuse;
        const Icon = config.icon;

        return (
          <div className={`flex items-center space-x-2 px-2 py-1 rounded-full ${config.bg}`}>
            <Icon className={`h-4 w-4 ${config.color}`} />
            <span className={`text-sm font-medium ${config.color}`}>
              {config.label}
            </span>
          </div>
        );
      }
    },
    {
      header: 'Motif',
      accessor: 'motif_refus',
      render: (passage) => passage.motif_refus || '-'
    }
  ];

  const passageStats = {
    total: passages.length,
    autorises: passages.filter(p => p.resultat === 'autorise').length,
    refuses: passages.filter(p => p.resultat === 'refuse').length,
    alertes: passages.filter(p => p.resultat === 'alerte').length
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Chargement...</div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">Journal des Passages</h1>
        <div className="flex items-center space-x-3">
          <div className="text-sm text-gray-500">
            Mise à jour automatique toutes les 5 secondes
          </div>
          <button
            onClick={handleExport}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>Exporter</span>
          </button>
        </div>
      </div>

      {error && (
        <Alert variant="error" className="mb-6" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Statistiques */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Passages</p>
                <p className="text-2xl font-bold text-gray-800">{passageStats.total}</p>
              </div>
              <Clock className="h-8 w-8 text-gray-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Autorisés</p>
                <p className="text-2xl font-bold text-green-600">{passageStats.autorises}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Refusés</p>
                <p className="text-2xl font-bold text-red-600">{passageStats.refuses}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Alertes</p>
                <p className="text-2xl font-bold text-orange-600">{passageStats.alertes}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtres */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filtres</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date début
              </label>
              <input
                type="date"
                value={filters.dateDebut}
                onChange={(e) => handleFilterChange('dateDebut', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date fin
              </label>
              <input
                type="date"
                value={filters.dateFin}
                onChange={(e) => handleFilterChange('dateFin', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Porte
              </label>
              <select
                value={filters.porte}
                onChange={(e) => handleFilterChange('porte', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="">Toutes les portes</option>
                {portes.map(porte => (
                  <option key={porte.id} value={porte.id}>{porte.nom}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Personnel
              </label>
              <select
                value={filters.personnel}
                onChange={(e) => handleFilterChange('personnel', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="">Tout le personnel</option>
                {personnels.map(personnel => (
                  <option key={personnel.id} value={personnel.id}>
                    {personnel.nom} {personnel.prenom}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Résultat
              </label>
              <select
                value={filters.resultat}
                onChange={(e) => handleFilterChange('resultat', e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">Tous les résultats</option>
                <option value="autorise">Autorisés</option>
                <option value="refuse">Refusés</option>
                <option value="alerte">Alertes</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table des passages */}
      <Card>
        <CardHeader>
          <CardTitle>Historique des Passages</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <DataTable
            data={passages}
            columns={columns}
            searchable={true}
            sortable={true}
            pagination={true}
            pageSize={15}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export default AccessLog;
