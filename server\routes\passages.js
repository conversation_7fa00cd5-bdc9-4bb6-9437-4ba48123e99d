const express = require('express');
const router = express.Router();
const { validationMiddleware, schemas } = require('../utils/validation');
const {
  enregistrerPassage,
  obtenirHistoriquePassages,
  obtenirPassagesRecents,
  obtenirStatistiquesPassages,
  verifierAccesBadge
} = require('../controllers/passageController');

/**
 * @route POST /api/passages
 * @desc Enregistrer un nouveau passage RFID
 * @access Public (lecteurs RFID)
 * @body epc_code - Code EPC du badge RFID
 * @body id_porte - ID de la porte
 * @body type_acces - Type d'accès (entree/sortie)
 */
router.post('/', 
  validationMiddleware(schemas.passage),
  enregistrerPassage
);

/**
 * @route GET /api/passages
 * @desc Récupérer l'historique des passages avec filtres
 * @access Public (à sécuriser plus tard)
 * @query page - Numéro de page (défaut: 1)
 * @query limit - Nombre d'éléments par page (défaut: 20)
 * @query type_acces - Type d'accès (entree/sortie)
 * @query resultat - Résultat (autorise/refuse/erreur)
 * @query id_porte - ID de la porte
 * @query date_debut - Date de début (ISO)
 * @query date_fin - Date de fin (ISO)
 * @query epc_code - Code EPC du badge (recherche partielle)
 * @query type_personnel - Type de personnel (militaire_interne/militaire_externe/civil_externe)
 */
router.get('/', obtenirHistoriquePassages);

/**
 * @route GET /api/passages/recent
 * @desc Récupérer les passages récents (temps réel)
 * @access Public (à sécuriser plus tard)
 * @query limit - Nombre d'éléments à retourner (défaut: 10)
 * @query minutes - Période en minutes (défaut: 60)
 */
router.get('/recent', obtenirPassagesRecents);

/**
 * @route GET /api/passages/statistiques
 * @desc Obtenir les statistiques des passages
 * @access Public (à sécuriser plus tard)
 * @query periode - Période en heures (défaut: 24)
 */
router.get('/statistiques', obtenirStatistiquesPassages);

/**
 * @route GET /api/passages/verifier/:epc_code
 * @desc Vérifier l'accès d'un badge (simulation lecteur RFID)
 * @access Public (lecteurs RFID)
 * @param epc_code - Code EPC du badge à vérifier
 */
router.get('/verifier/:epc_code', verifierAccesBadge);

/**
 * @route GET /api/passages/dashboard/recent
 * @desc Récupérer les passages récents pour le dashboard
 * @access Public (à sécuriser plus tard)
 * @returns Liste des passages récents avec informations essentielles
 */
router.get('/dashboard/recent', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const { Passage, Badge, Porte, AttributionBadge, Personnel, TypePersonnel } = require('../models');

    const passages = await Passage.findAll({
      include: [
        {
          model: Badge,
          as: 'badge',
          attributes: ['epc_code'],
          include: [{
            model: AttributionBadge,
            as: 'attributions',
            where: { statut: 'actif' },
            required: false,
            include: [{
              model: Personnel,
              as: 'personnel',
              attributes: ['nom', 'prenom', 'matricule', 'cin'],
              include: [{
                model: TypePersonnel,
                as: 'typePersonnel',
                attributes: ['nom_type']
              }]
            }]
          }]
        },
        {
          model: Porte,
          as: 'porte',
          attributes: ['libelle']
        }
      ],
      limit: parseInt(limit),
      order: [['date_acces', 'DESC']]
    });

    const result = passages.map(passage => {
      const attribution = passage.badge?.attributions?.[0];
      const personnel = attribution?.personnel;

      return {
        id: passage.id,
        date_acces: passage.date_acces,
        type_acces: passage.type_acces,
        resultat: passage.resultat,
        badge: passage.badge?.epc_code || 'Inconnu',
        porte: passage.porte?.libelle || 'Inconnue',
        personnel: personnel ? {
          nom: personnel.nom,
          prenom: personnel.prenom,
          type: personnel.typePersonnel?.nom_type
        } : null
      };
    });

    res.json(result);
  } catch (error) {
    console.error('Erreur lors de la récupération des passages récents:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération des passages récents',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route GET /api/passages/log/history
 * @desc Récupérer l'historique complet des passages pour le journal
 * @access Public (à sécuriser plus tard)
 * @returns Historique complet avec filtres et pagination
 */
router.get('/log/history', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      type_acces,
      resultat,
      id_porte,
      date_debut,
      date_fin,
      search
    } = req.query;
    const offset = (page - 1) * limit;

    const { Passage, Badge, Porte, AttributionBadge, Personnel, TypePersonnel } = require('../models');
    const { Op } = require('sequelize');

    let whereClause = {};

    // Filtres
    if (type_acces) whereClause.type_acces = type_acces;
    if (resultat) whereClause.resultat = resultat;
    if (id_porte) whereClause.id_porte = id_porte;

    // Filtre par date
    if (date_debut || date_fin) {
      whereClause.date_acces = {};
      if (date_debut) whereClause.date_acces[Op.gte] = new Date(date_debut);
      if (date_fin) whereClause.date_acces[Op.lte] = new Date(date_fin);
    }

    const { count, rows } = await Passage.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Badge,
          as: 'badge',
          attributes: ['epc_code'],
          include: [{
            model: AttributionBadge,
            as: 'attributions',
            where: { statut: 'actif' },
            required: false,
            include: [{
              model: Personnel,
              as: 'personnel',
              attributes: ['nom', 'prenom', 'matricule', 'cin'],
              include: [{
                model: TypePersonnel,
                as: 'typePersonnel',
                attributes: ['nom_type']
              }]
            }]
          }]
        },
        {
          model: Porte,
          as: 'porte',
          attributes: ['libelle']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['date_acces', 'DESC']]
    });

    const result = rows.map(passage => {
      const attribution = passage.badge?.attributions?.[0];
      const personnel = attribution?.personnel;

      return {
        id: passage.id,
        date_passage: passage.date_acces,
        type_acces: passage.type_acces,
        resultat: passage.resultat,
        badge_numero: passage.badge?.epc_code || 'Inconnu',
        porte_nom: passage.porte?.libelle || 'Inconnue',
        personnel: personnel ? {
          nom: personnel.nom,
          prenom: personnel.prenom,
          matricule: personnel.matricule,
          cin: personnel.cin,
          type: personnel.typePersonnel?.nom_type
        } : null
      };
    });

    res.json({
      passages: result,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'historique des passages:', error);
    res.status(500).json({
      message: 'Erreur lors de la récupération de l\'historique des passages',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
